#!/usr/bin/env python3
"""
项目清理脚本
清理开发过程中产生的临时文件、缓存文件等，为开源发布做准备
"""

import os
import shutil
import glob
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger=logging.getLogger(__name__)

def clean_python_cache():
    """清理Python缓存文件"""
    logger.info("🧹 清理Python缓存文件...")
    
    # 清理__pycache__目录
    for pycache_dir in Path('.').rglob('__pycache__'):
        if pycache_dir.is_dir():
            shutil.rmtree(pycache_dir)
            logger.info(f"   删除: {pycache_dir}")
    
    # 清理.pyc文件
    for pyc_file in Path('.').rglob('*.pyc'):
        pyc_file.unlink()
        logger.info(f"   删除: {pyc_file}")
    
    # 清理.pyo文件
    for pyo_file in Path('.').rglob('*.pyo'):
        pyo_file.unlink()
        logger.info(f"   删除: {pyo_file}")

def clean_temporary_files():
    """清理临时文件"""
    logger.info("🧹 清理临时文件...")
    
    temp_patterns=[
        '*.tmp', '*.temp', '*.bak', '*.lock',
        '*~', '*.swp', '*.swo', '.DS_Store',
        'Thumbs.db', '*.orig'
    ]
    
    for pattern in temp_patterns:
        for temp_file in Path('.').rglob(pattern):
            if temp_file.is_file():
                temp_file.unlink()
                logger.info(f"   删除: {temp_file}")

def clean_experiment_results():
    """清理实验结果（保留结构）"""
    logger.info("🧹 清理实验结果...")
    
    experiment_dirs=[
        'experiments/data',
        'experiments/log', 
        'experiments/pol_data',
        'experiments/results',
        'experiments/models',
        'experiments/temp'
    ]
    
    for exp_dir in experiment_dirs:
        exp_path=Path(exp_dir)
        if exp_path.exists():
            shutil.rmtree(exp_path)
            exp_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"   清理: {exp_dir}")
            
            # 创建.gitkeep文件保持目录结构
            gitkeep_file=exp_path / '.gitkeep'
            gitkeep_file.touch()

def clean_logs():
    """清理日志文件"""
    logger.info("🧹 清理日志文件...")
    
    for log_file in Path('.').rglob('*.log'):
        if log_file.is_file():
            log_file.unlink()
            logger.info(f"   删除: {log_file}")

def clean_build_artifacts():
    """清理构建产物"""
    logger.info("🧹 清理构建产物...")
    
    build_dirs=[
        'build', 'dist', '*.egg - info',
        '.pytest_cache', '.coverage',
        'htmlcov', '.tox'
    ]
    
    for pattern in build_dirs:
        for build_path in Path('.').glob(pattern):
            if build_path.is_dir():
                shutil.rmtree(build_path)
                logger.info(f"   删除目录: {build_path}")
            elif build_path.is_file():
                build_path.unlink()
                logger.info(f"   删除文件: {build_path}")

def clean_ide_files():
    """清理IDE配置文件"""
    logger.info("🧹 清理IDE配置文件...")
    
    ide_patterns=[
        '.vscode', '.idea', '*.sublime-*',
        '.spyderproject', '.ropeproject'
    ]
    
    for pattern in ide_patterns:
        for ide_path in Path('.').glob(pattern):
            if ide_path.is_dir():
                shutil.rmtree(ide_path)
                logger.info(f"   删除: {ide_path}")
            elif ide_path.is_file():
                ide_path.unlink()
                logger.info(f"   删除: {ide_path}")

def optimize_gitignore():
    """优化.gitignore文件"""
    logger.info("📝 优化.gitignore文件...")
    
    gitignore_content="""# VeryFL - PoL 项目忽略文件

# Python缓存和编译文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop - eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg - info/
.installed.cfg
*.egg

# 实验生成的文件
experiments/data/
experiments/log/
experiments/pol_data/
experiments/results/
experiments/models/
experiments/temp/
experiments/monitor*/
experiments/*_results*/

# 日志文件
*.log

# 临时文件
*.tmp
*.temp
*.bak
*.lock
*~
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# IDE配置
.vscode/
.idea/
*.sublime-*
.spyderproject
.ropeproject

# 测试和覆盖率
.pytest_cache/
.coverage
htmlcov/
.tox/

# 环境变量
.env
.venv
env/
venv/

# Jupyter Notebook
.ipynb_checkpoints

# 区块链相关
chainEnv/build/
chainEnv/reports/
node_modules/

# 但保留重要文件
!experiments/*.py
!experiments/*.md
!experiments/.gitkeep
"""
    
    with open('.gitignore', 'w', encoding='utf - 8') as f:
        f.write(gitignore_content)
    
    logger.info("   ✅ .gitignore已优化")

def create_gitkeep_files():
    """在重要的空目录中创建.gitkeep文件"""
    logger.info("📁 创建.gitkeep文件...")
    
    important_dirs=[
        'experiments/data',
        'experiments/log',
        'experiments/pol_data', 
        'experiments/results',
        'experiments/models',
        'experiments/temp'
    ]
    
    for dir_path in important_dirs:
        dir_obj=Path(dir_path)
        if dir_obj.exists():
            gitkeep_file=dir_obj / '.gitkeep'
            gitkeep_file.touch()
            logger.info(f"   创建: {gitkeep_file}")

def main():
    """主清理函数"""
    logger.info("🚀 开始项目清理，为开源发布做准备...")
    logger.info("=" * 60)
    
    # 执行清理步骤
    clean_python_cache()
    clean_temporary_files()
    clean_experiment_results()
    clean_logs()
    clean_build_artifacts()
    clean_ide_files()
    
    # 优化配置
    optimize_gitignore()
    create_gitkeep_files()
    
    logger.info("=" * 60)
    logger.info("✅ 项目清理完成！")
    logger.info("📦 项目已准备好开源发布")
    logger.info("")
    logger.info("🔍 清理内容:")
    logger.info("   - Python缓存文件 (__pycache__, *.pyc)")
    logger.info("   - 临时文件 (*.tmp, *.bak, etc.)")
    logger.info("   - 实验结果数据")
    logger.info("   - 日志文件")
    logger.info("   - 构建产物")
    logger.info("   - IDE配置文件")
    logger.info("")
    logger.info("📁 保留内容:")
    logger.info("   - 源代码文件")
    logger.info("   - 配置文件")
    logger.info("   - 文档文件")
    logger.info("   - 目录结构 (.gitkeep)")

if __name__== "__main__":
    main()
