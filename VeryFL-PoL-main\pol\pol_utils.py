"""
PoL工具函数模块
包含学习证明生成和验证所需的辅助函数
"""

import torch
import numpy as np
import hashlib
import os
import pickle
from typing import List, Dict, Any, Tuple
import logging

logger=logging.getLogger(__name__)

class PoLUtils:
    """学习证明工具类"""
    
    @staticmethod
    def get_parameters(model, numpy=False):
        """从PyTorch模型中提取参数"""
        parameter=torch.cat([i.data.reshape([-1]) for i in list(model.parameters())])
        if numpy:
            return parameter.cpu().numpy()
        else:
            return parameter
    
    @staticmethod
    def set_parameters(model, parameters, device):
        """将参数加载到PyTorch模型中"""
        param_idx=0
        for param in model.parameters():
            param_size=param.numel()
            param.data=parameters[param_idx: param_idx + param_size].view(param.shape).to(device)
            param_idx += param_size
        return model
    
    @staticmethod
    def parameter_distance(model1_params, model2_params, order=2):
        """计算两个模型参数之间的距离"""
        if not isinstance(order, list):
            orders=[order]
        else:
            orders = order
            
        res_list = []
        for o in orders:
            if o == 'inf':
                o = np.inf
            if o == 'cos' or o == 'cosine':
                res = (1 - torch.dot(model1_params, model2_params) /
                       (torch.norm(model1_params) * torch.norm(model2_params))).cpu().numpy()
            else:
                if o != np.inf:
                    try:
                        o=int(o)
                    except:
                        raise TypeError("输入的距离度量不可理解")
                res=torch.norm(model1_params - model2_params, p=o).cpu().numpy()
            
            if isinstance(res, np.ndarray):
                res=float(res)
            res_list.append(res)
        
        return res_list
    
    @staticmethod
    def compute_hash(data):
        """计算数据的哈希值"""
        if isinstance(data, torch.Tensor):
            data=data.cpu().numpy()
        if isinstance(data, np.ndarray):
            data=data.tobytes()
        elif isinstance(data, (list, tuple)):
            data=str(data).encode()
        elif isinstance(data, str):
            data=data.encode()
        
        return hashlib.sha256(data).hexdigest()
    
    @staticmethod
    def save_checkpoint(model, optimizer, step, save_path):
        """保存训练检查点"""
        state={
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict() if optimizer else None,
            'step': step
        }
        torch.save(state, save_path)
        return save_path
    
    @staticmethod
    def load_checkpoint(checkpoint_path, model, optimizer=None):
        """加载训练检查点"""
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")
        
        state=torch.load(checkpoint_path)
        model.load_state_dict(state['model'])
        if optimizer and 'optimizer' in state and state['optimizer']:
            optimizer.load_state_dict(state['optimizer'])
        
        return state.get('step', 0)
    
    @staticmethod
    def save_pol_data(pol_data, save_path):
        """保存PoL数据到文件"""
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'wb') as f:
            pickle.dump(pol_data, f)
        logger.info(f"PoL数据已保存到: {save_path}")
    
    @staticmethod
    def load_pol_data(load_path):
        """从文件加载PoL数据"""
        if not os.path.exists(load_path):
            logger.warning(f"PoL数据文件不存在: {load_path}，返回空数据")
            return None

        try:
            with open(load_path, 'rb') as f:
                pol_data=pickle.load(f)
            logger.info(f"PoL数据已从 {load_path} 加载")
            return pol_data
        except Exception as e:
            logger.error(f"加载PoL数据失败: {load_path}, 错误: {e}")
            return None
    
    @staticmethod
    def create_batch_indices_sequence(dataloader, num_epochs=1):
        """创建批次索引序列"""
        indices=[]
        for epoch in range(num_epochs):
            for batch_idx, (data, target) in enumerate(dataloader):
                # 记录批次索引和数据哈希
                batch_info={
                    'epoch': epoch,
                    'batch_idx': batch_idx,
                    'data_hash': PoLUtils.compute_hash(data),
                    'target_hash': PoLUtils.compute_hash(target)
                }
                indices.append(batch_info)
        return indices
    
    @staticmethod
    def verify_batch_sequence(dataloader, recorded_indices, num_epochs=1):
        """验证批次序列的一致性"""
        current_indices=PoLUtils.create_batch_indices_sequence(dataloader, num_epochs)
        
        if len(current_indices) != len(recorded_indices):
            return False
        
        for i, (current, recorded) in enumerate(zip(current_indices, recorded_indices)):
            if current['data_hash'] != recorded['data_hash']:
                logger.warning(f"批次 {i} 的数据哈希不匹配")
                return False
            if current['target_hash'] != recorded['target_hash']:
                logger.warning(f"批次 {i} 的标签哈希不匹配")
                return False
        
        return True
