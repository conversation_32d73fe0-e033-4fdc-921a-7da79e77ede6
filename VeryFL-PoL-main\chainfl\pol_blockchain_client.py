"""
PoL区块链客户端
负责PoL证明与真实区块链的交互
"""

import logging
import hashlib
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger=logging.getLogger(__name__)

@dataclass
class PoLSubmissionResult:
    """PoL提交结果"""
    success: bool
    tx_hash: Optional[str] = None
    error_message: Optional[str] = None
    gas_used: Optional[int] = None

@dataclass
class ClientReputation:
    """客户端信誉信息"""
    total_submissions: int
    valid_submissions: int
    reputation_score: int
    is_blacklisted: bool

class PoLBlockchainClient:
    """PoL区块链客户端"""
    
    def __init__(self, auto_init=True):
        """初始化区块链客户端"""
        self.pol_manager_contract=None
        self.accounts = []
        self.network = None
        self._initialized = False

        if auto_init:
            self._initialize_blockchain()
    
    def _ensure_initialized(self):
        """确保区块链已初始化"""
        if not self._initialized:
            self._initialize_blockchain()

    def _initialize_blockchain(self):
        """初始化真实区块链连接"""
        if self._initialized:
            return

        try:
            from brownie import project, network, accounts

            # 加载项目，避免重复加载
            try:
                loaded_projects=project.get_loaded_projects()
                if loaded_projects:
                    p=loaded_projects[0]  # 获取已加载的项目
                    logger.info("使用已加载的Brownie项目")
                else:
                    raise IndexError("No loaded projects")
            except (IndexError, AttributeError):
                # 确保项目路径正确
                import os

                # 尝试多个可能的路径
                possible_paths=[
                    os.path.join(os.path.dirname(__file__), '..', 'chainEnv'),
                    os.path.join(os.getcwd(), 'chainEnv'),
                    os.path.join(os.getcwd(), 'VeryFL', 'VeryFL - main', 'chainEnv'),
                    './chainEnv',
                    'chainEnv'
                ]

                project_path=None
                for path in possible_paths:
                    abs_path = os.path.abspath(path)
                    if os.path.exists(abs_path) and os.path.exists(os.path.join(abs_path, 'contracts')):
                        project_path=abs_path
                        break

                if project_path is None:
                    raise RuntimeError(f"无法找到chainEnv项目目录。尝试的路径: {possible_paths}")

                logger.info(f"找到Brownie项目路径: {project_path}")
                p=project.load(project_path)
                logger.info(f"成功加载Brownie项目: {project_path}")

            # 连接到网络，指定具体的网络配置
            if not network.is_connected():
                # 尝试连接到本地ganache
                try:
                    network.connect('development')
                except:
                    # 如果development不存在，尝试连接到ganache - local
                    network.connect('ganache - local')

            self.network=network
            self.accounts = accounts

            logger.info(f"连接到网络: {network.show_active()}")
            logger.info(f"可用账户数量: {len(accounts)}")

            # 部署或连接SimplePoLManager合约，添加重试机制解决并发部署问题
            if len(p.SimplePoLManager) == 0:
                logger.info("开始部署SimplePoLManager合约...")

                # 添加重试机制解决Gas价格竞争
                max_retries=3
                base_gas_price = ***********  # 25 Gwei基础价格

                for attempt in range(max_retries):
                    try:
                        # 动态调整Gas价格，每次重试增加10%
                        gas_price=int(base_gas_price * (1.1 ** attempt))

                        deploy_config={
                            'from': accounts[0],
                            'gas_limit': 8000000,  # 增加Gas限制以确保部署成功
                            'gas_price': gas_price,
                            'allow_revert': True,
                        }

                        logger.info(f"尝试部署合约 (第{attempt + 1}次)，Gas价格: {gas_price/1e9:.1f} Gwei")
                        self.pol_manager_contract=p.SimplePoLManager.deploy(deploy_config)
                        logger.info(f"✅ SimplePoLManager合约部署成功: {self.pol_manager_contract.address}")
                        break

                    except Exception as e:
                        if "transaction underpriced" in str(e) and attempt < max_retries - 1:
                            logger.warning(f"⚠️ 合约部署失败 (Gas价格过低)，第{attempt + 1}次重试...")
                            time.sleep(1)  # 等待1秒后重试
                            continue
                        elif attempt== max_retries - 1:
                            logger.error(f"❌ SimplePoLManager合约部署失败 (已重试{max_retries}次): {e}")
                            raise
                        else:
                            logger.warning(f"⚠️ 合约部署失败，第{attempt + 1}次重试: {e}")
                            time.sleep(1)
                            continue
            else:
                self.pol_manager_contract=p.SimplePoLManager[-1]
                logger.info(f"连接到已存在的SimplePoLManager合约: {self.pol_manager_contract.address}")

            self._initialized=True

        except Exception as e:
            logger.error(f"区块链初始化失败: {e}")
            raise RuntimeError(f"无法连接到区块链: {e}")
    
    def submit_pol_proof(self, client_address: str, pol_proof: Dict[str, Any],
                        ipfs_hash: Optional[str] = None) -> PoLSubmissionResult:
        """提交PoL证明到区块链"""
        self._ensure_initialized()
        try:
            pol_hash=self._compute_pol_hash(pol_proof)
            total_steps=pol_proof.get('total_steps', 0)
            
            # 转换哈希格式
            pol_hash_bytes=bytes.fromhex(pol_hash)
            
            # 为客户端分配账户
            client_account=self._get_client_account(client_address)

            # 调用智能合约 - 使用客户端对应的账户提交
            # 重要：记录实际使用的区块链地址
            actual_blockchain_address=client_account.address

            # 添加更详细的日志
            logger.debug(f"提交PoL证明: 客户端={client_address}, 步数={total_steps}, 哈希={pol_hash[: 16]}...")

            # 提交交易时使用一致的Gas配置 - 修复Gas价格
            tx_config={
                'from': client_account,
                'gas_limit': 500000,  # 为普通交易设置合理的Gas限制
                'gas_price': ***********,  # 25 Gwei，与部署配置一致
            }

            tx=self.pol_manager_contract.submitPoL(
                pol_hash_bytes,
                ipfs_hash or "",
                total_steps,
                tx_config
            )

            # 改进的交易确认机制 - 修复NoneType错误
            try:
                # 等待交易确认，使用默认超时设置
                receipt=tx.wait(1)  # 等待1个确认，与brownie配置一致
                if receipt and hasattr(receipt, 'gas_used'):
                    logger.info(f"客户端 {client_address} PoL证明提交成功，Gas消耗: {receipt.gas_used}")
                    gas_used=receipt.gas_used
                else:
                    logger.warning(f"交易确认返回了空receipt，尝试手动获取...")
                    receipt=self.network.web3.eth.get_transaction_receipt(tx.txid)
                    gas_used=receipt.gasUsed
                    logger.info(f"通过手动获取receipt，Gas消耗: {gas_used}")
            except Exception as wait_error:
                logger.warning(f"等待交易确认超时，但交易可能已成功: {wait_error}")
                # 尝试获取交易状态
                try:
                    receipt=self.network.web3.eth.get_transaction_receipt(tx.txid)
                    gas_used=receipt.gasUsed
                    logger.info(f"通过receipt获取到交易状态，Gas消耗: {gas_used}")
                except Exception as receipt_error:
                    gas_used=None
                    logger.warning(f"无法获取交易receipt: {receipt_error}，但交易可能仍然成功")
            
            return PoLSubmissionResult(
                success=True,
                tx_hash=tx.txid,
                gas_used=gas_used
            )
            
        except Exception as e:
            logger.error(f"提交PoL证明失败: {e}")
            return PoLSubmissionResult(
                success=False,
                error_message=str(e)
            )
    
    def verify_pol_proof(self, client_address: str, record_index: int,
                        is_valid: bool, verifier_address: str) -> bool:
        """验证PoL证明"""
        try:
            # 使用一致的Gas配置 - 修复Gas价格
            tx_config={
                'from': self.accounts[0],
                'gas_limit': 300000,
                'gas_price': ***********,  # 25 Gwei，与部署配置一致
            }

            tx=self.pol_manager_contract.verifyPoL(
                client_address,
                record_index,
                is_valid,
                tx_config
            )

            # 改进的确认机制 - 移除不兼容的timeout参数
            try:
                tx.wait(1)  # 等待1个确认，与brownie配置一致
                logger.info(f"客户端 {client_address} PoL验证完成")
                return True
            except Exception as wait_error:
                logger.warning(f"验证交易确认超时: {wait_error}")
                # 检查交易是否实际成功
                try:
                    receipt=self.network.web3.eth.get_transaction_receipt(tx.txid)
                    if receipt.status== 1:
                        logger.info(f"客户端 {client_address} PoL验证完成（通过receipt确认）")
                        return True
                    else:
                        logger.error(f"验证交易失败，status: {receipt.status}")
                        return False
                except:
                    logger.error("无法获取验证交易状态")
                    return False

        except Exception as e:
            logger.error(f"验证PoL证明失败: {e}")
            return False

    def batch_verify_pol_proofs(self, verifications: List[Dict[str, Any]], batch_size: int=10) -> List[bool]:
        """
        批量验证PoL证明 - 性能优化版本

        Args:
            verifications: 验证数据列表，每个包含 {client_address, record_index, is_valid, verifier_address}
            batch_size: 批量大小

        Returns:
            验证结果列表
        """
        if not verifications:
            return []

        logger.info(f"🔗 开始批量验证 {len(verifications)} 个PoL证明 (批量大小: {batch_size})")

        results=[]
        total_batches = (len(verifications) + batch_size - 1) // batch_size

        for batch_idx in range(total_batches):
            start_idx=batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(verifications))
            batch=verifications[start_idx: end_idx]

            logger.debug(f"处理批次 {batch_idx + 1}/{total_batches}: {len(batch)} 个验证")

            # 批量提交交易
            batch_results=self._submit_verification_batch(batch)
            results.extend(batch_results)

        success_count=sum(results)
        logger.info(f"🎉 批量验证完成: {success_count}/{len(verifications)} 成功")

        return results

    def _submit_verification_batch(self, batch: List[Dict[str, Any]]) -> List[bool]:
        """提交一个批次的验证"""
        batch_results=[]

        # 简化为串行提交，避免批量提交的复杂性
        for verification in batch:
            try:
                # 使用原有的单个验证方法，已经验证可靠
                success = self.verify_pol_proof(
                    verification['client_address'],
                    verification['record_index'],
                    verification['is_valid'],
                    verification.get('verifier_address', self.accounts[0])
                )
                batch_results.append(success)

                if success:
                    logger.debug(f"✅ 批量验证成功: {verification['client_address']}")
                else:
                    logger.warning(f"❌ 批量验证失败: {verification['client_address']}")

            except Exception as e:
                logger.error(f"批量验证异常 {verification['client_address']}: {e}")
                batch_results.append(False)

        return batch_results

    def batch_submit_pol_proofs(self, submissions: List[Dict[str, Any]]) -> List[PoLSubmissionResult]:
        """批量提交PoL证明 - 性能优化的关键"""
        self._ensure_initialized()
        results=[]

        logger.info(f"🚀 开始批量提交 {len(submissions)} 个PoL证明")

        # 第一阶段：并行准备所有交易（不等待确认）
        pending_txs=[]
        for submission in submissions:
            try:
                client_address = submission['client_address']
                pol_proof = submission['pol_proof']
                ipfs_hash = submission.get('ipfs_hash')

                pol_hash=self._compute_pol_hash(pol_proof)
                total_steps=pol_proof.get('total_steps', 0)
                pol_hash_bytes=bytes.fromhex(pol_hash)
                client_account=self._get_client_account(client_address)

                tx_config={
                    'from': client_account,
                    'gas_limit': 500000,
                    'gas_price': ***********,
                }

                # 提交交易但不等待确认
                tx=self.pol_manager_contract.submitPoL(
                    pol_hash_bytes,
                    ipfs_hash or "",
                    total_steps,
                    tx_config
                )

                pending_txs.append({
                    'client_address': client_address,
                    'tx': tx,
                    'submission_index': len(pending_txs)
                })

                logger.debug(f"   📤 已提交客户端 {client_address} 的交易，等待确认...")

            except Exception as e:
                logger.error(f"提交客户端 {submission['client_address']} 的PoL证明失败: {e}")
                results.append(PoLSubmissionResult(
                    success=False,
                    error_message=str(e)
                ))

        # 第二阶段：批量等待所有交易确认
        logger.info(f"   ⏳ 批量等待 {len(pending_txs)} 个交易确认...")

        for pending_tx in pending_txs:
            try:
                receipt=pending_tx['tx'].wait(1)  # 等待1个确认，与brownie配置一致
                results.append(PoLSubmissionResult(
                    success=True,
                    tx_hash=pending_tx['tx'].txid,
                    gas_used=receipt.gas_used
                ))
                logger.debug(f"   ✅ 客户端 {pending_tx['client_address']} 交易确认成功")

            except Exception as e:
                logger.warning(f"客户端 {pending_tx['client_address']} 交易确认失败: {e}")
                results.append(PoLSubmissionResult(
                    success=False,
                    error_message=str(e)
                ))

        logger.info(f"🎉 批量提交完成：{len([r for r in results if r.success])}/{len(results)} 成功")
        return results

    def batch_submit_pol_proofs_with_timeout(self, submissions: List[Dict[str, Any]], timeout: int=30) -> List[PoLSubmissionResult]:
        """带超时的批量提交PoL证明 - 防止无限阻塞"""
        self._ensure_initialized()
        results=[]

        logger.info(f"🚀 开始带超时的批量提交 {len(submissions)} 个PoL证明 (超时: {timeout}秒)")

        import time
        import threading
        from concurrent.futures import ThreadPoolExecutor, TimeoutError, as_completed

        start_time=time.time()

        def submit_single_with_timeout(submission):
            """提交单个PoL证明，带内部超时"""
            try:
                client_address=submission['client_address']
                pol_proof = submission['pol_proof']
                ipfs_hash = submission.get('ipfs_hash')

                # 使用较短的超时进行单个提交
                result=self._submit_single_pol_with_timeout(client_address, pol_proof, ipfs_hash, timeout=10)
                return result

            except Exception as e:
                logger.error(f"提交客户端 {submission.get('client_id', 'unknown')} 的PoL证明失败: {e}")
                return PoLSubmissionResult(
                    success=False,
                    error_message=str(e)
                )

        # 使用线程池并行提交，但有总体超时控制
        try:
            with ThreadPoolExecutor(max_workers=min(len(submissions), 5)) as executor:
                # 提交所有任务
                future_to_submission={
                    executor.submit(submit_single_with_timeout, submission): submission
                    for submission in submissions
                }

                # 收集结果，但有超时限制
                for future in as_completed(future_to_submission, timeout=timeout):
                    try:
                        result=future.result(timeout = 5)  # 每个结果最多等5秒
                        results.append(result)
                    except Exception as e:
                        logger.warning(f"获取提交结果时出错: {e}")
                        results.append(PoLSubmissionResult(
                            success=False,
                            error_message=f"结果获取超时: {str(e)}"
                        ))

        except TimeoutError:
            logger.warning(f"批量提交总体超时 ({timeout}秒)，已完成 {len(results)}/{len(submissions)} 个提交")
            # 为未完成的提交添加超时结果
            while len(results) < len(submissions):
                results.append(PoLSubmissionResult(
                    success=False,
                    error_message="批量提交超时"
                ))

        except Exception as e:
            logger.error(f"批量提交过程中发生异常: {e}")
            # 确保返回正确数量的结果
            while len(results) < len(submissions):
                results.append(PoLSubmissionResult(
                    success=False,
                    error_message=f"批量提交异常: {str(e)}"
                ))

        elapsed_time=time.time() - start_time
        success_count=len([r for r in results if r.success])
        logger.info(f"🎉 带超时的批量提交完成：{success_count}/{len(results)} 成功，耗时 {elapsed_time:.2f}秒")

        return results

    def _submit_single_pol_with_timeout(self, client_address: str, pol_proof: Dict[str, Any],
                                       ipfs_hash: Optional[str] = None, timeout: int=10) -> PoLSubmissionResult:
        """提交单个PoL证明，带超时控制"""
        try:
            pol_hash=self._compute_pol_hash(pol_proof)
            total_steps=pol_proof.get('total_steps', 0)
            pol_hash_bytes=bytes.fromhex(pol_hash)
            client_account=self._get_client_account(client_address)

            logger.debug(f"提交PoL证明: 客户端={client_address}, 步数={total_steps}, 哈希={pol_hash[: 16]}...")

            tx_config={
                'from': client_account,
                'gas_limit': 500000,
                'gas_price': ***********,
            }

            # 提交交易
            tx=self.pol_manager_contract.submitPoL(
                pol_hash_bytes,
                ipfs_hash or "",
                total_steps,
                tx_config
            )

            # 带超时的交易确认
            try:
                import signal

                def timeout_handler(signum, frame):
                    raise TimeoutError(f"交易确认超时 ({timeout}秒)")

                # 使用线程安全的超时机制，避免signal在多线程环境中的问题
                try:
                    import threading
                    import time

                    result={'receipt': None, 'error': None}

                    def wait_for_receipt():
                        try:
                            result['receipt'] = tx.wait(1)  # 等待1个确认
                        except Exception as e:
                            result['error'] = str(e)

                    # 使用线程进行超时控制
                    wait_thread=threading.Thread(target = wait_for_receipt)
                    wait_thread.daemon=True
                    wait_thread.start()
                    wait_thread.join(timeout)

                    if wait_thread.is_alive():
                        # 超时了
                        raise TimeoutError(f"交易确认超时 ({timeout}秒)")

                    if result['error']:
                        raise Exception(result['error'])

                    receipt=result['receipt']
                    gas_used = receipt.gas_used if receipt and hasattr(receipt, 'gas_used') else None
                    logger.debug(f"客户端 {client_address} PoL证明提交成功，Gas消耗: {gas_used}")

                except (ImportError, AttributeError):
                    # 如果线程不可用，使用简单的wait
                    receipt=tx.wait(1)
                    gas_used=receipt.gas_used if receipt and hasattr(receipt, 'gas_used') else None

            except (TimeoutError, Exception) as wait_error:
                logger.warning(f"交易确认超时或失败: {wait_error}")
                # 尝试获取交易状态，但不等待太久
                try:
                    receipt=self.network.web3.eth.get_transaction_receipt(tx.txid)
                    gas_used=receipt.gasUsed
                    logger.debug(f"通过receipt获取到交易状态，Gas消耗: {gas_used}")
                except:
                    gas_used=None
                    logger.warning(f"无法获取交易receipt，但交易可能已成功")

            return PoLSubmissionResult(
                success=True,
                tx_hash=tx.txid,
                gas_used=gas_used
            )

        except Exception as e:
            logger.error(f"提交PoL证明失败: {e}")
            return PoLSubmissionResult(
                success=False,
                error_message=str(e)
            )

    def batch_verify_pol_proofs(self, verifications: List[Dict[str, Any]]) -> List[bool]:
        """批量验证PoL证明 - 性能优化的关键"""
        results=[]

        logger.info(f"🔍 开始批量验证 {len(verifications)} 个PoL证明")

        # 第一阶段：并行提交所有验证交易
        pending_txs=[]
        for verification in verifications:
            try:
                client_address = verification['client_address']
                record_index = verification['record_index']
                is_valid = verification['is_valid']
                verifier_address = verification.get('verifier_address', 'system')

                tx_config={
                    'from': self.accounts[0],
                    'gas_limit': 300000,
                    'gas_price': ***********,
                }

                tx=self.pol_manager_contract.verifyPoL(
                    client_address,
                    record_index,
                    is_valid,
                    tx_config
                )

                pending_txs.append({
                    'client_address': client_address,
                    'tx': tx
                })

                logger.debug(f"   📋 已提交客户端 {client_address} 的验证交易")

            except Exception as e:
                logger.error(f"提交客户端 {verification['client_address']} 的验证交易失败: {e}")
                results.append(False)

        # 第二阶段：批量等待所有验证交易确认
        logger.info(f"   ⏳ 批量等待 {len(pending_txs)} 个验证交易确认...")

        for pending_tx in pending_txs:
            try:
                pending_tx['tx'].wait(1)  # 等待1个确认，与brownie配置一致
                results.append(True)
                logger.debug(f"   ✅ 客户端 {pending_tx['client_address']} 验证确认成功")

            except Exception as e:
                logger.warning(f"客户端 {pending_tx['client_address']} 验证确认失败: {e}")
                results.append(False)

        logger.info(f"🎉 批量验证完成：{sum(results)}/{len(results)} 成功")
        return results

    def get_client_reputation(self, client_address: str) -> ClientReputation:
        """获取客户端信誉信息"""
        self._ensure_initialized()
        try:
            reputation_data=self.pol_manager_contract.getClientReputation(client_address)

            # 检查返回数据的长度
            if len(reputation_data) >= 3:
                return ClientReputation(
                    total_submissions=int(reputation_data[0]),
                    valid_submissions=int(reputation_data[1]),
                    reputation_score=int(reputation_data[2]),
                    is_blacklisted=False  # SimplePoLManager没有黑名单功能
                )
            else:
                logger.warning(f"客户端 {client_address} 信誉数据格式异常")
                return ClientReputation(0, 0, 500, False)

        except Exception as e:
            logger.error(f"获取客户端信誉失败: {e}")
            return ClientReputation(0, 0, 500, False)
    
    def get_pol_records(self, client_address: str) -> List[Dict[str, Any]]:
        """获取客户端的PoL记录"""
        self._ensure_initialized()
        try:
            record_count=self.pol_manager_contract.getClientPoLCount(client_address)
            records=[]

            logger.info(f"客户端 {client_address} 有 {record_count} 条PoL记录")

            for i in range(record_count):
                try:
                    record=self.pol_manager_contract.clientPoLRecords(client_address, i)
                    records.append({
                        'pol_hash': record[0].hex() if hasattr(record[0], 'hex') else str(record[0]),
                        'ipfs_hash': record[1] if len(record) > 1 else '',
                        'timestamp': int(record[2]) if len(record) > 2 else 0,
                        'total_steps': int(record[3]) if len(record) > 3 else 0,
                        'is_verified': bool(record[4]) if len(record) > 4 else False,
                        'is_valid': bool(record[5]) if len(record) > 5 else False,
                        'verifier': record[6] if len(record) > 6 else ''
                    })
                except Exception as e:
                    logger.warning(f"获取记录 {i} 失败: {e}")
                    continue

            return records

        except Exception as e:
            logger.error(f"获取PoL记录失败: {e}")
            return []
    
    def distribute_rewards(self, client_rewards: Dict[str, int]) -> bool:
        """分配激励奖励"""
        self._ensure_initialized()
        try:
            if not client_rewards:
                logger.info("没有客户端需要分配奖励")
                return True

            # 检查激励池余额
            pool_info=self.get_incentive_pool_info()
            total_rewards_needed=sum(client_rewards.values())
            available_rewards=pool_info['total_rewards'] - pool_info['distributed_rewards']

            if total_rewards_needed > available_rewards:
                logger.warning(f"激励池余额不足: 需要 {total_rewards_needed}, 可用 {available_rewards}")
                return False

            clients=list(client_rewards.keys())
            rewards=list(client_rewards.values())

            # 使用一致的Gas配置 - 修复Gas价格
            tx_config={
                'from': self.accounts[0],
                'gas_limit': 500000,  # 分配奖励可能需要更多Gas
                'gas_price': ***********,  # 25 Gwei，与部署配置一致
            }

            tx=self.pol_manager_contract.distributeRewards(
                clients,
                rewards,
                tx_config
            )

            # 改进的确认机制 - 移除不兼容的timeout参数
            try:
                tx.wait(1)  # 等待1个确认，与brownie配置一致
                logger.info(f"激励奖励分配完成: {len(clients)} 个客户端（通过PoL验证）")
                return True
            except Exception as wait_error:
                logger.warning(f"奖励分配交易确认超时: {wait_error}")
                # 检查交易是否实际成功
                try:
                    receipt=self.network.web3.eth.get_transaction_receipt(tx.txid)
                    if receipt.status== 1:
                        logger.info(f"激励奖励分配完成（通过receipt确认）: {len(clients)} 个客户端")
                        return True
                    else:
                        logger.error(f"奖励分配交易失败，status: {receipt.status}")
                        return False
                except:
                    logger.error("无法获取奖励分配交易状态")
                    return False

        except Exception as e:
            logger.error(f"分配奖励失败: {e}")
            return False
    
    def add_to_incentive_pool(self, amount: int):
        """向激励池添加奖励"""
        try:
            # 使用一致的Gas配置 - 修复Gas价格
            tx_config={
                'from': self.accounts[0],
                'value': amount,
                'gas_limit': 300000,
                'gas_price': ***********,  # 25 Gwei，与部署配置一致
            }

            tx=self.pol_manager_contract.addToIncentivePool(tx_config)

            # 改进的确认机制 - 移除不兼容的timeout参数
            try:
                tx.wait(1)  # 等待1个确认，与brownie配置一致
                logger.info(f"向激励池添加奖励: {amount}")
            except Exception as wait_error:
                logger.warning(f"激励池交易确认超时: {wait_error}")
                # 检查交易是否实际成功
                try:
                    receipt=self.network.web3.eth.get_transaction_receipt(tx.txid)
                    if receipt.status== 1:
                        logger.info(f"向激励池添加奖励成功（通过receipt确认）: {amount}")
                    else:
                        logger.error(f"激励池交易失败，status: {receipt.status}")
                except:
                    logger.error("无法获取激励池交易状态")

        except Exception as e:
            logger.error(f"添加激励池奖励失败: {e}")
    
    def start_new_round(self):
        """开始新一轮"""
        try:
            # 使用一致的Gas配置 - 修复Gas价格
            tx_config={
                'from': self.accounts[0],
                'gas_limit': 200000,
                'gas_price': ***********,  # 25 Gwei，与部署配置一致
            }

            tx=self.pol_manager_contract.startNewRound(tx_config)

            # 改进的确认机制 - 移除不兼容的timeout参数
            try:
                tx.wait(1)  # 等待1个确认，与brownie配置一致
                logger.info("开始新一轮")
            except Exception as wait_error:
                logger.warning(f"新轮次交易确认超时: {wait_error}")
                # 检查交易是否实际成功
                try:
                    receipt=self.network.web3.eth.get_transaction_receipt(tx.txid)
                    if receipt.status== 1:
                        logger.info("开始新一轮（通过receipt确认）")
                    else:
                        logger.error(f"新轮次交易失败，status: {receipt.status}")
                except:
                    logger.error("无法获取新轮次交易状态")
            
        except Exception as e:
            logger.error(f"开始新一轮失败: {e}")
    
    def get_incentive_pool_info(self) -> Dict[str, int]:
        """获取激励池信息"""
        try:
            pool_info=self.pol_manager_contract.getIncentivePoolInfo()
            
            return {
                'total_rewards': pool_info[0],
                'distributed_rewards': pool_info[1],
                'current_round': pool_info[2]
            }
            
        except Exception as e:
            logger.error(f"获取激励池信息失败: {e}")
            return {'total_rewards': 0, 'distributed_rewards': 0, 'current_round': 1}
    
    def _compute_pol_hash(self, pol_proof: Dict[str, Any]) -> str:
        """计算PoL证明哈希"""
        key_info={
            'client_id': pol_proof.get('client_id'),
            'total_steps': pol_proof.get('total_steps'),
            'proof_hash': pol_proof.get('proof_hash'),
            'checkpoint_count': len(pol_proof.get('checkpoints', [])),
            'timestamp': self._get_current_timestamp()
        }
        
        content=json.dumps(key_info, sort_keys=True)
        return hashlib.sha256(content.encode()).hexdigest()
    
    def _get_current_timestamp(self) -> int:
        """获取当前时间戳"""
        import time
        return int(time.time())

    def _get_client_account(self, client_address: str):
        """为客户端分配区块链账户"""
        # 修复：使用确定性但无冲突的账户分配策略

        # 如果还没有客户端地址映射表，创建一个
        if not hasattr(self, '_client_account_mapping'):
            self._client_account_mapping={}
            self._next_account_index = 1  # 从accounts[1]开始分配

        # 如果客户端已经有分配的账户，直接返回
        if client_address in self._client_account_mapping:
            account_index = self._client_account_mapping[client_address]
            client_account = self.accounts[account_index]
            logger.debug(f"客户端 {client_address} 使用已分配的账户 {account_index}")
            return client_account

        # 确保有足够的账户
        available_accounts=len(self.accounts) - 1  # 保留accounts[0]用于合约部署
        if available_accounts < 50:  # 确保至少有50个客户端账户
            logger.warning(f"当前可用账户数: {available_accounts}，建议启动Ganache时使用 --accounts 100")

        # 为新客户端分配下一个可用账户
        if self._next_account_index <= available_accounts:
            account_index=self._next_account_index
            self._client_account_mapping[client_address] = account_index
            self._next_account_index += 1
            logger.info(f"为客户端 {client_address} 分配新账户 {account_index}")
        else:
            logger.error(f"账户不足！客户端数量超过可用账户数 {available_accounts}")
            # 回退到哈希分配（可能有冲突，但至少能运行）
            import hashlib
            hash_obj=hashlib.md5(client_address.encode())
            hash_int=int(hash_obj.hexdigest(), 16)
            account_index=(hash_int % available_accounts) + 1
            logger.warning(f"客户端 {client_address} 使用哈希分配账户 {account_index}（可能有冲突）")

        client_account=self.accounts[account_index]

        # 确保客户端账户有足够的ETH
        if client_account.balance() < 1e18:  # 少于1 ETH
            # 从accounts[0]转账给客户端账户
            self.accounts[0].transfer(client_account, "10 ether")
            logger.info(f"为客户端账户 {client_account.address} 转账 10 ETH")

        return client_account

# 全局区块链客户端实例（延迟初始化）
pol_blockchain_client=PoLBlockchainClient(auto_init = False)
