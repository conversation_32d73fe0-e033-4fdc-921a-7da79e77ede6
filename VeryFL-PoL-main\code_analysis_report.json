{"total_files": 88, "total_issues": 201, "issue_types": {"duplicate_import": 74, "long_line": 19, "todo_comment": 29, "unused_parameter": 51, "empty_except": 9, "string_concatenation": 19}, "issues": [{"file": "batch_experiments.py", "type": "duplicate_import", "import": "run.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"file": "task.py", "type": "duplicate_import", "import": "threading"}, {"file": "task.py", "type": "duplicate_import", "import": "threading"}, {"file": "task.py", "type": "duplicate_import", "import": "time"}, {"file": "task.py", "type": "duplicate_import", "import": "time"}, {"file": "task.py", "type": "duplicate_import", "import": "concurrent.futures.TimeoutError"}, {"file": "task.py", "type": "duplicate_import", "import": "time"}, {"file": "task.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "task.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "task.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "task.py", "type": "duplicate_import", "import": "threading"}, {"file": "task.py", "type": "duplicate_import", "import": "time"}, {"file": "task.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "task.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "task.py", "type": "duplicate_import", "import": "threading"}, {"file": "task.py", "type": "duplicate_import", "import": "time"}, {"file": "run.py", "type": "duplicate_import", "import": "os"}, {"file": "run.py", "type": "duplicate_import", "import": "sys"}, {"file": "run.py", "type": "duplicate_import", "import": "os"}, {"file": "run.py", "type": "duplicate_import", "import": "subprocess"}, {"file": "run.py", "type": "duplicate_import", "import": "sys"}, {"file": "run.py", "type": "duplicate_import", "import": "os"}, {"file": "run.py", "type": "duplicate_import", "import": "json"}, {"file": "run.py", "type": "duplicate_import", "import": "subprocess"}, {"file": "run.py", "type": "duplicate_import", "import": "sys"}, {"file": "run.py", "type": "duplicate_import", "import": "os"}, {"file": "run.py", "type": "duplicate_import", "import": "json"}, {"file": "run.py", "type": "duplicate_import", "import": "subprocess"}, {"file": "run.py", "type": "duplicate_import", "import": "sys"}, {"file": "run.py", "type": "duplicate_import", "import": "os"}, {"file": "run.py", "type": "duplicate_import", "import": "json"}, {"file": "run.py", "type": "duplicate_import", "import": "torch"}, {"file": "run.py", "type": "duplicate_import", "import": "torch"}, {"file": "chainfl/connection_manager.py", "type": "duplicate_import", "import": "time"}, {"file": "chainfl/connection_manager.py", "type": "duplicate_import", "import": "brownie.project"}, {"file": "chainfl/connection_manager.py", "type": "duplicate_import", "import": "os"}, {"file": "chainfl/pol_blockchain_client.py", "type": "duplicate_import", "import": "time"}, {"file": "chainfl/pol_blockchain_client.py", "type": "duplicate_import", "import": "time"}, {"file": "chainfl/pol_blockchain_client.py", "type": "duplicate_import", "import": "<PERSON><PERSON><PERSON>"}, {"file": "chainfl/pol_blockchain_client.py", "type": "duplicate_import", "import": "threading"}, {"file": "chainfl/pol_blockchain_client.py", "type": "duplicate_import", "import": "time"}, {"file": "client/polClient.py", "type": "duplicate_import", "import": "time"}, {"file": "client/polClient.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "client/polClient.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "client/polClient.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "experiments/ablation_study.py", "type": "duplicate_import", "import": "logging"}, {"file": "experiments/compression_comparison.py", "type": "duplicate_import", "import": "time"}, {"file": "experiments/compression_comparison.py", "type": "duplicate_import", "import": "torch"}, {"file": "experiments/experiment_framework.py", "type": "duplicate_import", "import": "re"}, {"file": "experiments/experiment_monitor.py", "type": "duplicate_import", "import": "torch"}, {"file": "experiments/parallel_executor.py", "type": "duplicate_import", "import": "torch"}, {"file": "experiments/parallel_executor.py", "type": "duplicate_import", "import": "gpu_manager.release_gpu_for_experiment"}, {"file": "experiments/parallel_executor.py", "type": "duplicate_import", "import": "gpu_manager.get_gpu_status"}, {"file": "experiments/sota_comparison.py", "type": "duplicate_import", "import": "logging"}, {"file": "experiments/sota_comparison.py", "type": "duplicate_import", "import": "time"}, {"file": "experiments/sota_comparison.py", "type": "duplicate_import", "import": "task.Task"}, {"file": "experiments/sota_comparison.py", "type": "duplicate_import", "import": "task.Task"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "torch"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "copy.deepcopy"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "time"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "numpy"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "threading"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "torch"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "torch.utils.data.DataLoader"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "threading"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "traceback"}, {"file": "pol/pol_verifier.py", "type": "duplicate_import", "import": "traceback"}, {"file": "server/aggregation_alg/polAggregator.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "server/aggregation_alg/polAggregator.py", "type": "duplicate_import", "import": "torch"}, {"file": "server/aggregation_alg/polAggregator.py", "type": "duplicate_import", "import": "threading"}, {"file": "server/aggregation_alg/polAggregator.py", "type": "duplicate_import", "import": "time"}, {"file": "server/aggregation_alg/polAggregator.py", "type": "duplicate_import", "import": "chainfl.pol_blockchain_client.pol_blockchain_client"}, {"file": "util/reproducibility.py", "type": "duplicate_import", "import": "torch"}, {"file": "util/reproducibility.py", "type": "duplicate_import", "import": "numpy"}, {"file": "batch_experiments.py", "line": 227, "type": "long_line", "length": 122}, {"file": "download_datasets.py", "line": 201, "type": "long_line", "length": 134}, {"file": "task.py", "line": 200, "type": "long_line", "length": 131}, {"file": "task.py", "line": 222, "type": "long_line", "length": 123}, {"file": "task.py", "line": 319, "type": "long_line", "length": 123}, {"file": "task.py", "line": 69, "type": "todo_comment", "content": "self.attack_detection_stats['total_clients'] += 1  # TODO: 考虑使用join()优化"}, {"file": "task.py", "line": 72, "type": "todo_comment", "content": "self.attack_detection_stats['true_positives'] += 1  # TODO: 考虑使用join()优化"}, {"file": "task.py", "line": 73, "type": "todo_comment", "content": "self.attack_detection_stats['detected_attacks'] += 1  # TODO: 考虑使用join()优化"}, {"file": "task.py", "line": 75, "type": "todo_comment", "content": "self.attack_detection_stats['false_negatives'] += 1  # TODO: 考虑使用join()优化"}, {"file": "task.py", "line": 77, "type": "todo_comment", "content": "self.attack_detection_stats['false_positives'] += 1  # TODO: 考虑使用join()优化"}, {"file": "task.py", "line": 79, "type": "todo_comment", "content": "self.attack_detection_stats['true_negatives'] += 1  # TODO: 考虑使用join()优化"}, {"file": "debug_and_clean.py", "line": 170, "type": "todo_comment", "content": "# 检查TODO/FIXME注释"}, {"file": "debug_and_clean.py", "line": 173, "type": "todo_comment", "content": "if any(keyword in line_lower for keyword in ['todo', 'fixme', 'hack', 'xxx']):"}, {"file": "debug_and_clean.py", "line": 177, "type": "todo_comment", "content": "'type': 'todo_comment',"}, {"file": "debug_and_clean.py", "line": 190, "type": "todo_comment", "content": "todo_comments=[i for i in quality_issues if i.get('type') == 'todo_comment']"}, {"file": "debug_and_clean.py", "line": 195, "type": "todo_comment", "content": "if todo_comments:"}, {"file": "debug_and_clean.py", "line": 196, "type": "todo_comment", "content": "logger.warning(f\"   ⚠️ 发现 {len(todo_comments)} 个待办注释:\")"}, {"file": "debug_and_clean.py", "line": 197, "type": "todo_comment", "content": "for comment in todo_comments[: 5]:  # 只显示前5个"}, {"file": "debug_and_clean.py", "line": 200, "type": "todo_comment", "content": "if not long_lines and not todo_comments:"}, {"file": "debug_and_clean.py", "line": 281, "type": "todo_comment", "content": "if '+=' in content and 'str' in content:  # TODO: 考虑使用join()优化"}, {"file": "debug_and_clean.py", "line": 284, "type": "todo_comment", "content": "if '+=' in line and any(quote in line for quote in ['\"', \"'\"]):  # TODO: 考虑使用join()优化"}, {"file": "auto_fix_code.py", "line": 103, "type": "todo_comment", "content": "if '+=' in stripped and any(quote in stripped for quote in ['\"', \"'\"]):  # TODO: 考虑使用join()优化"}, {"file": "auto_fix_code.py", "line": 105, "type": "todo_comment", "content": "if '# TODO: 考虑使用join()优化' not in line:"}, {"file": "auto_fix_code.py", "line": 106, "type": "todo_comment", "content": "lines[i] = line + '  # TODO: 考虑使用join()优化'"}, {"file": "chainfl/pol_blockchain_client.py", "line": 401, "type": "long_line", "length": 128}, {"file": "client/clients.py", "line": 59, "type": "todo_comment", "content": "result += f\"{key}: {value} \"  # TODO: 考虑使用join()优化"}, {"file": "client/base/baseTrainer.py", "line": 38, "type": "long_line", "length": 128}, {"file": "experiments/compression_comparison.py", "line": 257, "type": "long_line", "length": 122}, {"file": "experiments/compression_comparison.py", "line": 388, "type": "long_line", "length": 163}, {"file": "experiments/compression_comparison.py", "line": 393, "type": "long_line", "length": 126}, {"file": "experiments/experiment_monitor.py", "line": 218, "type": "long_line", "length": 166}, {"file": "experiments/experiment_monitor.py", "line": 219, "type": "long_line", "length": 191}, {"file": "experiments/experiment_monitor.py", "line": 220, "type": "long_line", "length": 186}, {"file": "experiments/master_experiment_runner.py", "line": 73, "type": "long_line", "length": 121}, {"file": "experiments/sota_comparison.py", "line": 317, "type": "long_line", "length": 121}, {"file": "model/resnet.py", "line": 95, "type": "long_line", "length": 122}, {"file": "model/resnet.py", "line": 98, "type": "long_line", "length": 122}, {"file": "model/resnet.py", "line": 101, "type": "long_line", "length": 122}, {"file": "server/aggregation_alg/polAggregator.py", "line": 113, "type": "todo_comment", "content": "self.verification_stats['failed_clients'] += 1  # TODO: 考虑使用join()优化"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 126, "type": "todo_comment", "content": "self.verification_stats['verified_clients'] += 1  # TODO: 考虑使用join()优化"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 129, "type": "todo_comment", "content": "self.verification_stats['failed_clients'] += 1  # TODO: 考虑使用join()优化"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 323, "type": "todo_comment", "content": "self.verification_stats['no_pol_clients'] += 1  # TODO: 考虑使用join()优化"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 327, "type": "todo_comment", "content": "self.verification_stats['no_pol_clients'] += 1  # TODO: 考虑使用join()优化"}, {"file": "server/base/baseAggregator.py", "line": 33, "type": "long_line", "length": 131}, {"file": "util/path_manager.py", "line": 167, "type": "todo_comment", "content": "log_name += '.log'  # TODO: 考虑使用join()优化"}, {"file": "util/path_manager.py", "line": 174, "type": "todo_comment", "content": "analysis_name += f'.{file_type}'  # TODO: 考虑使用join()优化"}, {"file": "util/path_manager.py", "line": 181, "type": "todo_comment", "content": "plot_name += f'.{file_type}'  # TODO: 考虑使用join()优化"}, {"file": "util/path_manager.py", "line": 188, "type": "todo_comment", "content": "report_name += f'.{file_type}'  # TODO: 考虑使用join()优化"}, {"file": "analyze_results.py", "line": 222, "type": "unused_parameter", "parameter": "timestamps", "function": "_analyze_trends"}, {"file": "analyze_results.py", "line": 298, "type": "unused_parameter", "parameter": "analysis", "function": "generate_plots"}, {"file": "download_datasets.py", "line": 33, "type": "unused_parameter", "parameter": "name", "function": "download_dataset_with_retry"}, {"file": "task.py", "line": 66, "type": "unused_parameter", "parameter": "client_id", "function": "record_attack_detection"}, {"file": "verify_datasets.py", "line": 99, "type": "empty_except", "message": "空的异常处理块"}, {"file": "run.py", "line": 47, "type": "unused_parameter", "parameter": "exp_id", "function": "simple_assign_gpu"}, {"file": "run.py", "line": 47, "type": "unused_parameter", "parameter": "client_num", "function": "simple_assign_gpu"}, {"file": "run.py", "line": 47, "type": "unused_parameter", "parameter": "model", "function": "simple_assign_gpu"}, {"file": "run.py", "line": 51, "type": "unused_parameter", "parameter": "exp_id", "function": "simple_release_gpu"}, {"file": "chainfl/pol_blockchain_client.py", "line": 225, "type": "unused_parameter", "parameter": "verifier_address", "function": "verify_pol_proof"}, {"file": "chainfl/pol_blockchain_client.py", "line": 506, "type": "unused_parameter", "parameter": "signum", "function": "timeout_handler"}, {"file": "chainfl/pol_blockchain_client.py", "line": 506, "type": "unused_parameter", "parameter": "frame", "function": "timeout_handler"}, {"file": "client/clients.py", "line": 157, "type": "unused_parameter", "parameter": "epoch", "function": "train"}, {"file": "client/clients.py", "line": 173, "type": "unused_parameter", "parameter": "watermarks", "function": "train"}, {"file": "client/base/baseTrainer.py", "line": 56, "type": "unused_parameter", "parameter": "epoch", "function": "_train_epoch"}, {"file": "client/base/baseTrainer.py", "line": 87, "type": "unused_parameter", "parameter": "epoch", "function": "_on_before_upload"}, {"file": "client/base/baseTrainer.py", "line": 94, "type": "unused_parameter", "parameter": "epoch", "function": "_on_after_download"}, {"file": "client/base/baseTrainer.py", "line": 121, "type": "unused_parameter", "parameter": "epoch", "function": "_download_model"}, {"file": "client/trainer/MoonTrainer.py", "line": 18, "type": "unused_parameter", "parameter": "epoch", "function": "_train_epoch_moon"}, {"file": "client/trainer/MoonTrainer.py", "line": 67, "type": "unused_parameter", "parameter": "epoch", "function": "_train_epoch"}, {"file": "client/trainer/SignTrainer.py", "line": 18, "type": "unused_parameter", "parameter": "epoch", "function": "_train_epoch"}, {"file": "client/trainer/fedproxTrainer.py", "line": 23, "type": "unused_parameter", "parameter": "epoch", "function": "_train_epoch"}, {"file": "client/trainer/normalTrainer.py", "line": 14, "type": "unused_parameter", "parameter": "epoch", "function": "_train_epoch"}, {"file": "client/trainer/polTrainer.py", "line": 94, "type": "unused_parameter", "parameter": "batch_offset", "function": "_train_epoch_with_pol"}, {"file": "client/trainer/polTrainer.py", "line": 163, "type": "unused_parameter", "parameter": "epoch", "function": "_on_before_upload"}, {"file": "client/trainer/polTrainer.py", "line": 167, "type": "unused_parameter", "parameter": "epoch", "function": "_on_after_download"}, {"file": "client/trainer/polTrainer.py", "line": 186, "type": "unused_parameter", "parameter": "epoch", "function": "_download_model"}, {"file": "client/trainer/polTrainer.py", "line": 200, "type": "unused_parameter", "parameter": "batch_offset", "function": "_train_epoch_with_pol"}, {"file": "dataset/DatasetSpliter.py", "line": 21, "type": "unused_parameter", "parameter": "dataset", "function": "_sample_random"}, {"file": "dataset/DatasetSpliter.py", "line": 21, "type": "unused_parameter", "parameter": "client_list", "function": "_sample_random"}, {"file": "experiments/attack_simulator.py", "line": 79, "type": "unused_parameter", "parameter": "dataloader", "function": "_free_rider_attack"}, {"file": "experiments/attack_simulator.py", "line": 79, "type": "unused_parameter", "parameter": "optimizer", "function": "_free_rider_attack"}, {"file": "experiments/attack_simulator.py", "line": 79, "type": "unused_parameter", "parameter": "criterion", "function": "_free_rider_attack"}, {"file": "experiments/attack_simulator.py", "line": 196, "type": "unused_parameter", "parameter": "client_id", "function": "_pol_based_detection"}, {"file": "experiments/attack_simulator.py", "line": 196, "type": "unused_parameter", "parameter": "model_update", "function": "_pol_based_detection"}, {"file": "experiments/attack_simulator.py", "line": 221, "type": "unused_parameter", "parameter": "client_id", "function": "_statistical_detection"}, {"file": "experiments/experiment_framework.py", "line": 205, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/experiment_framework.py", "line": 218, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/experiment_monitor.py", "line": 102, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/experiment_monitor.py", "line": 152, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/network_simulator.py", "line": 150, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/network_simulator.py", "line": 503, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/network_simulator.py", "line": 433, "type": "unused_parameter", "parameter": "network_config", "function": "_run_real_federated_learning"}, {"file": "experiments/parallel_executor.py", "line": 238, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/parallel_executor.py", "line": 258, "type": "empty_except", "message": "空的异常处理块"}, {"file": "experiments/sota_comparison.py", "line": 261, "type": "unused_parameter", "parameter": "dataset", "function": "_run_baseline_experiment"}, {"file": "experiments/sota_comparison.py", "line": 261, "type": "unused_parameter", "parameter": "client_num", "function": "_run_baseline_experiment"}, {"file": "experiments/sota_comparison.py", "line": 261, "type": "unused_parameter", "parameter": "batch_size", "function": "_run_baseline_experiment"}, {"file": "experiments/sota_comparison.py", "line": 261, "type": "unused_parameter", "parameter": "learning_rate", "function": "_run_baseline_experiment"}, {"file": "experiments/sota_comparison.py", "line": 261, "type": "unused_parameter", "parameter": "local_epochs", "function": "_run_baseline_experiment"}, {"file": "model/resnet.py", "line": 62, "type": "unused_parameter", "parameter": "name", "function": "__init__"}, {"file": "model/resnet.py", "line": 62, "type": "unused_parameter", "parameter": "created_time", "function": "__init__"}, {"file": "model/simple.py", "line": 17, "type": "unused_parameter", "parameter": "coefficient_transfer", "function": "copy_params"}, {"file": "server/serverSimulator.py", "line": 77, "type": "unused_parameter", "parameter": "params", "function": "download_model"}, {"file": "server/base/baseAggregator.py", "line": 26, "type": "unused_parameter", "parameter": "raw_client_model_or_grad_list", "function": "_aggregate_alg"}, {"file": "server/base/baseAggregator.py", "line": 40, "type": "unused_parameter", "parameter": "raw_client_model_or_grad_list", "function": "_on_before_aggregation"}, {"file": "server/base/baseAggregator.py", "line": 63, "type": "unused_parameter", "parameter": "test_data", "function": "test"}, {"file": "server/base/baseAggregator.py", "line": 63, "type": "unused_parameter", "parameter": "device", "function": "test"}, {"file": "server/defence_alg/defence_util.py", "line": 14, "type": "unused_parameter", "parameter": "raw_client_model_or_grad_list", "function": "alg1"}, {"file": "server/defence_alg/defence_util.py", "line": 16, "type": "unused_parameter", "parameter": "raw_client_model_or_grad_list", "function": "alg2"}, {"file": "task.py", "line": 69, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "task.py", "line": 72, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "task.py", "line": 73, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "task.py", "line": 75, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "task.py", "line": 77, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "task.py", "line": 79, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "debug_and_clean.py", "line": 281, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "debug_and_clean.py", "line": 284, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "auto_fix_code.py", "line": 103, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "client/clients.py", "line": 59, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 113, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 126, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 129, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 323, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "server/aggregation_alg/polAggregator.py", "line": 327, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "util/path_manager.py", "line": 167, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "util/path_manager.py", "line": 174, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "util/path_manager.py", "line": 181, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}, {"file": "util/path_manager.py", "line": 188, "type": "string_concatenation", "suggestion": "考虑使用join()或f - string"}]}