# 贡献指南

感谢您对VeryFL-PoL项目的关注！我们欢迎各种形式的贡献。

## 🚀 快速开始

### 环境要求
- Python 3.8+
- PyTorch 1.13+
- Node.js 16+ (用于区块链环境)

### 安装依赖
```bash
pip install -r requirements.txt
npm install -g ganache-cli
```

### 运行测试
```bash
# 启动区块链环境
./start_ganache.sh

# 运行基础测试
python run.py demo
```

## 📝 代码规范

### Python代码风格
- 使用中文注释和文档字符串
- 遵循PEP 8规范
- 函数和类名使用英文，注释使用中文

### 提交规范
- 提交信息使用中文
- 格式：`类型: 简短描述`
- 类型：功能、修复、文档、重构、测试

示例：
```
功能: 添加新的PoL验证算法
修复: 解决GPU内存泄漏问题
文档: 更新安装指南
```

## 🔧 开发流程

### 1. Fork项目
点击右上角的Fork按钮

### 2. 克隆代码
```bash
git clone https://github.com/your-username/VeryFL-PoL.git
cd VeryFL-PoL
```

### 3. 创建分支
```bash
git checkout -b feature/your-feature-name
```

### 4. 开发和测试
```bash
# 开发您的功能
# 运行测试确保没有破坏现有功能
python run.py test
```

### 5. 提交代码
```bash
git add .
git commit -m "功能: 添加您的功能描述"
git push origin feature/your-feature-name
```

### 6. 创建Pull Request
在GitHub上创建Pull Request

## 🧪 测试指南

### 运行完整测试
```bash
# 基础功能测试
python run.py demo

# PoL模块测试
python -c "from pol import PoLGenerator, PoLVerifier; print('PoL模块正常')"

# 设备管理测试
python -c "from util.device_manager import get_optimal_device_config; print('设备管理正常')"
```

### 添加新测试
在相应模块中添加测试函数，确保：
- 测试覆盖主要功能
- 测试用例清晰易懂
- 包含边界条件测试

## 📚 文档贡献

### 文档类型
- **README.md**: 项目概述和快速开始
- **QUICK_START.md**: 详细的使用指南
- **代码注释**: 函数和类的详细说明

### 文档规范
- 使用中文编写
- 包含代码示例
- 保持简洁清晰

## 🐛 问题报告

### 报告Bug
请包含以下信息：
- 操作系统和Python版本
- 错误信息和堆栈跟踪
- 重现步骤
- 预期行为

### 功能请求
请描述：
- 功能的用途和价值
- 具体的实现建议
- 相关的学术论文或参考资料

## 🎯 贡献重点

我们特别欢迎以下方面的贡献：

### 算法改进
- PoL验证算法优化
- 新的防御机制
- 性能优化

### 实验扩展
- 新的数据集支持
- 更多的攻击类型
- 实验分析工具

### 工程优化
- 代码重构和优化
- 文档完善
- 测试覆盖率提升

## 📞 联系方式

如有问题，请通过以下方式联系：
- 创建GitHub Issue
- 发起Discussion讨论

## 📄 许可证

通过贡献代码，您同意您的贡献将在与项目相同的许可证下发布。

---

再次感谢您的贡献！🎉
