#!/usr/bin/env python3
"""
VeryFL - PoL项目全面Debug和代码清理脚本
检查语法错误、逻辑错误、未使用的导入等代码质量问题
"""

import os
import sys
import ast
import logging
import subprocess
from pathlib import Path
from typing import List, Dict, Set, Tuple
import importlib.util

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger=logging.getLogger(__name__)

class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self, project_root: str="."):
        self.project_root=Path(project_root)
        self.python_files=list(self.project_root.rglob("*.py"))
        self.issues=[]
        
    def analyze_all(self):
        """执行所有分析"""
        logger.info("🔍 开始全面代码分析...")
        logger.info("=" * 60)
        
        # 1. 语法检查
        self.check_syntax_errors()
        
        # 2. 导入分析
        self.analyze_imports()
        
        # 3. 代码质量检查
        self.check_code_quality()
        
        # 4. 逻辑错误检查
        self.check_logic_errors()
        
        # 5. 性能问题检查
        self.check_performance_issues()
        
        # 6. 生成报告
        self.generate_report()
        
    def check_syntax_errors(self):
        """检查语法错误"""
        logger.info("📝 检查语法错误...")
        
        syntax_errors=[]
        for py_file in self.python_files:
            try:
                with open(py_file, 'r', encoding='utf - 8') as f:
                    content=f.read()
                
                # 尝试解析AST
                ast.parse(content, filename=str(py_file))
                
            except SyntaxError as e:
                syntax_errors.append({
                    'file': str(py_file),
                    'line': e.lineno,
                    'error': str(e)
                })
            except Exception as e:
                syntax_errors.append({
                    'file': str(py_file),
                    'line': 0,
                    'error': f"解析错误: {str(e)}"
                })
        
        if syntax_errors:
            logger.error(f"   ❌ 发现 {len(syntax_errors)} 个语法错误:")
            for error in syntax_errors:
                logger.error(f"      {error['file']}:{error['line']} - {error['error']}")
            self.issues.extend(syntax_errors)
        else:
            logger.info("   ✅ 没有发现语法错误")
    
    def analyze_imports(self):
        """分析导入问题"""
        logger.info("\n📦 分析导入问题...")
        
        import_issues=[]
        
        for py_file in self.python_files:
            try:
                with open(py_file, 'r', encoding='utf - 8') as f:
                    content=f.read()
                
                tree=ast.parse(content, filename=str(py_file))
                
                # 收集所有导入
                imports=[]
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        module=node.module or ""
                        for alias in node.names:
                            imports.append(f"{module}.{alias.name}")
                
                # 检查未使用的导入（简化版）
                used_names=set()
                for node in ast.walk(tree):
                    if isinstance(node, ast.Name):
                        used_names.add(node.id)
                
                # 检查重复导入
                seen_imports=set()
                for imp in imports:
                    if imp in seen_imports:
                        import_issues.append({
                            'file': str(py_file),
                            'type': 'duplicate_import',
                            'import': imp
                        })
                    seen_imports.add(imp)
                        
            except Exception as e:
                import_issues.append({
                    'file': str(py_file),
                    'type': 'analysis_error',
                    'error': str(e)
                })
        
        if import_issues:
            logger.warning(f"   ⚠️ 发现 {len(import_issues)} 个导入问题:")
            for issue in import_issues[: 10]:  # 只显示前10个
                if issue['type'] == 'duplicate_import':
                    logger.warning(f"      {issue['file']} - 重复导入: {issue['import']}")
                else:
                    logger.warning(f"      {issue['file']} - {issue.get('error', '未知错误')}")
            if len(import_issues) > 10:
                logger.warning(f"      ... 还有 {len(import_issues) - 10} 个问题")
        else:
            logger.info("   ✅ 没有发现明显的导入问题")
        
        self.issues.extend(import_issues)
    
    def check_code_quality(self):
        """检查代码质量问题"""
        logger.info("\n🔧 检查代码质量...")
        
        quality_issues=[]
        
        for py_file in self.python_files:
            try:
                with open(py_file, 'r', encoding='utf - 8') as f:
                    lines=f.readlines()
                
                # 检查长行
                for i, line in enumerate(lines, 1):
                    if len(line.strip()) > 120:
                        quality_issues.append({
                            'file': str(py_file),
                            'line': i,
                            'type': 'long_line',
                            'length': len(line.strip())
                        })
                
                # 检查TODO/FIXME注释
                for i, line in enumerate(lines, 1):
                    line_lower=line.lower()
                    if any(keyword in line_lower for keyword in ['todo', 'fixme', 'hack', 'xxx']):
                        quality_issues.append({
                            'file': str(py_file),
                            'line': i,
                            'type': 'todo_comment',
                            'content': line.strip()
                        })
                
            except Exception as e:
                quality_issues.append({
                    'file': str(py_file),
                    'type': 'analysis_error',
                    'error': str(e)
                })
        
        # 统计问题类型
        long_lines=[i for i in quality_issues if i.get('type') == 'long_line']
        todo_comments=[i for i in quality_issues if i.get('type') == 'todo_comment']
        
        if long_lines:
            logger.warning(f"   ⚠️ 发现 {len(long_lines)} 行过长的代码 (>120字符)")
        
        if todo_comments:
            logger.warning(f"   ⚠️ 发现 {len(todo_comments)} 个待办注释:")
            for comment in todo_comments[: 5]:  # 只显示前5个
                logger.warning(f"      {comment['file']}:{comment['line']} - {comment['content'][: 50]}...")
        
        if not long_lines and not todo_comments:
            logger.info("   ✅ 代码质量检查通过")
        
        self.issues.extend(quality_issues)
    
    def check_logic_errors(self):
        """检查潜在的逻辑错误"""
        logger.info("\n🧠 检查逻辑错误...")
        
        logic_issues=[]
        
        for py_file in self.python_files:
            try:
                with open(py_file, 'r', encoding='utf - 8') as f:
                    content=f.read()
                
                tree=ast.parse(content, filename=str(py_file))
                
                # 检查空的except块
                for node in ast.walk(tree):
                    if isinstance(node, ast.ExceptHandler):
                        if not node.body or (len(node.body) == 1 and isinstance(node.body[0], ast.Pass)):
                            logic_issues.append({
                                'file': str(py_file),
                                'line': node.lineno,
                                'type': 'empty_except',
                                'message': '空的异常处理块'
                            })
                
                # 检查未使用的变量（简化版）
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        # 检查函数参数是否被使用
                        param_names=[arg.arg for arg in node.args.args]
                        used_names = set()
                        
                        for child in ast.walk(node):
                            if isinstance(child, ast.Name) and isinstance(child.ctx, ast.Load):
                                used_names.add(child.id)
                        
                        for param in param_names:
                            if param not in used_names and param not in ['self', 'cls', 'args', 'kwargs']:
                                logic_issues.append({
                                    'file': str(py_file),
                                    'line': node.lineno,
                                    'type': 'unused_parameter',
                                    'parameter': param,
                                    'function': node.name
                                })
                
            except Exception as e:
                logic_issues.append({
                    'file': str(py_file),
                    'type': 'analysis_error',
                    'error': str(e)
                })
        
        if logic_issues:
            logger.warning(f"   ⚠️ 发现 {len(logic_issues)} 个潜在逻辑问题:")
            for issue in logic_issues[: 10]:  # 只显示前10个
                if issue['type'] == 'empty_except':
                    logger.warning(f"      {issue['file']}:{issue['line']} - {issue['message']}")
                elif issue['type'] == 'unused_parameter':
                    logger.warning(f"      {issue['file']}:{issue['line']} - 未使用的参数: {issue['parameter']} in {issue['function']}")
        else:
            logger.info("   ✅ 没有发现明显的逻辑错误")
        
        self.issues.extend(logic_issues)
    
    def check_performance_issues(self):
        """检查性能问题"""
        logger.info("\n⚡ 检查性能问题...")
        
        performance_issues=[]
        
        for py_file in self.python_files:
            try:
                with open(py_file, 'r', encoding='utf - 8') as f:
                    content=f.read()
                
                # 检查字符串拼接
                if '+=' in content and 'str' in content:  # TODO: 考虑使用join()优化
                    lines=content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if '+=' in line and any(quote in line for quote in ['"', "'"]):  # TODO: 考虑使用join()优化
                            performance_issues.append({
                                'file': str(py_file),
                                'line': i,
                                'type': 'string_concatenation',
                                'suggestion': '考虑使用join()或f - string'
                            })
                
                # 检查循环中的列表操作
                tree=ast.parse(content, filename=str(py_file))
                for node in ast.walk(tree):
                    if isinstance(node, (ast.For, ast.While)):
                        for child in ast.walk(node):
                            if isinstance(child, ast.Call) and isinstance(child.func, ast.Attribute):
                                if child.func.attr== 'append' and isinstance(child.func.value, ast.Name):
                                    # 这是一个简化的检查，实际应该更复杂
                                    pass
                
            except Exception as e:
                performance_issues.append({
                    'file': str(py_file),
                    'type': 'analysis_error',
                    'error': str(e)
                })
        
        if performance_issues:
            logger.warning(f"   ⚠️ 发现 {len(performance_issues)} 个潜在性能问题")
        else:
            logger.info("   ✅ 没有发现明显的性能问题")
        
        self.issues.extend(performance_issues)
    
    def generate_report(self):
        """生成分析报告"""
        logger.info("\n📊 生成分析报告...")
        
        # 按类型统计问题
        issue_types={}
        for issue in self.issues:
            issue_type=issue.get('type', 'unknown')
            issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
        
        logger.info("=" * 60)
        logger.info("📋 代码分析报告")
        logger.info("=" * 60)
        logger.info(f"📁 分析文件数: {len(self.python_files)}")
        logger.info(f"🔍 发现问题数: {len(self.issues)}")
        
        if issue_types:
            logger.info("\n📊 问题分类:")
            for issue_type, count in sorted(issue_types.items()):
                logger.info(f"   {issue_type}: {count}")
        
        # 保存详细报告
        report_path=self.project_root / "code_analysis_report.json"
        import json
        with open(report_path, 'w', encoding='utf - 8') as f:
            json.dump({
                'total_files': len(self.python_files),
                'total_issues': len(self.issues),
                'issue_types': issue_types,
                'issues': self.issues
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 详细报告已保存: {report_path}")
        
        if len(self.issues) == 0:
            logger.info("\n🎉 恭喜！代码质量检查全部通过！")
        elif len(self.issues) < 10:
            logger.info("\n✅ 代码质量良好，只有少量问题需要关注")
        else:
            logger.warning("\n⚠️ 发现较多问题，建议逐步修复")


def run_external_tools():
    """运行外部代码检查工具"""
    logger.info("\n🛠️ 运行外部代码检查工具...")
    
    tools=[
        ('flake8', 'flake8 --max-line - length=120 --ignore = E501, W503 .'),
        ('pylint', 'pylint --disable=C0103, C0111 --max-line - length=120 *.py'),
    ]
    
    for tool_name, command in tools:
        try:
            result=subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode== 0:
                logger.info(f"   ✅ {tool_name}: 检查通过")
            else:
                logger.warning(f"   ⚠️ {tool_name}: 发现问题")
                if result.stdout:
                    logger.warning(f"      输出: {result.stdout[: 200]}...")
                    
        except subprocess.TimeoutExpired:
            logger.warning(f"   ⏰ {tool_name}: 检查超时")
        except FileNotFoundError:
            logger.info(f"   ℹ️ {tool_name}: 未安装，跳过")
        except Exception as e:
            logger.warning(f"   ❌ {tool_name}: 检查失败 - {e}")


def main():
    """主函数"""
    import argparse
    
    parser=argparse.ArgumentParser(description="VeryFL - PoL代码分析和清理")
    parser.add_argument('--external-tools', action='store_true', help="运行外部检查工具")
    parser.add_argument('--fix-imports', action='store_true', help="自动修复导入问题")
    
    args=parser.parse_args()
    
    # 执行代码分析
    analyzer=CodeAnalyzer()
    analyzer.analyze_all()
    
    # 运行外部工具
    if args.external_tools:
        run_external_tools()
    
    logger.info("\n🎯 建议的修复优先级:")
    logger.info("1. 修复语法错误（如果有）")
    logger.info("2. 处理空的异常处理块")
    logger.info("3. 清理未使用的导入和变量")
    logger.info("4. 优化性能问题")
    logger.info("5. 处理代码风格问题")


if __name__== "__main__":
    main()
