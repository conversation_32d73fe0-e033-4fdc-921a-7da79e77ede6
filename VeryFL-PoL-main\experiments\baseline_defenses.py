#!/usr/bin/env python3
"""
主流防御方法实现
用于与我们的PoL方法进行对比
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Dict, Any, Tuple
import logging
from scipy import stats

logger=logging.getLogger(__name__)

class BaselineDefenses:
    """主流防御方法集合"""
    
    @staticmethod
    def fedavg_aggregation(client_updates: List[Dict[str, torch.Tensor]], 
                          client_weights: List[float] = None) -> Dict[str, torch.Tensor]:
        """标准FedAvg聚合（无防御）"""
        if not client_updates:
            return {}
        
        if client_weights is None:
            client_weights=[1.0 / len(client_updates)] * len(client_updates)
        
        # 加权平均
        aggregated={}
        for key in client_updates[0].keys():
            weighted_sum=torch.zeros_like(client_updates[0][key])
            for i, update in enumerate(client_updates):
                weighted_sum += client_weights[i] * update[key]
            aggregated[key] = weighted_sum
        
        return aggregated
    
    @staticmethod
    def krum_aggregation(client_updates: List[Dict[str, torch.Tensor]], 
                        num_malicious: int=0, multi_krum: bool=False) -> Dict[str, torch.Tensor]:
        """
        Krum聚合方法
        参考: Machine Learning with Adversaries: Byzantine Tolerant Gradient Descent (NIPS 2017)
        """
        if not client_updates:
            return {}
        
        n=len(client_updates)
        if num_malicious== 0:
            num_malicious = n // 3  # 默认假设1/3是恶意的
        
        # 将模型参数展平为向量
        client_vectors = []
        for update in client_updates:
            vector = torch.cat([param.flatten() for param in update.values()])
            client_vectors.append(vector)
        
        # 计算每个客户端的Krum分数
        scores=[]
        for i in range(n):
            distances=[]
            for j in range(n):
                if i != j:
                    dist=torch.norm(client_vectors[i] - client_vectors[j]).item()
                    distances.append(dist)
            
            # 选择最近的n - num_malicious - 2个距离
            distances.sort()
            k=n - num_malicious - 2
            score = sum(distances[: k])
            scores.append(score)
        
        if multi_krum:
            # Multi - Krum: 选择分数最低的几个客户端
            num_selected=n - num_malicious
            selected_indices = np.argsort(scores)[: num_selected]
            
            # 对选中的客户端进行平均
            selected_updates=[client_updates[i] for i in selected_indices]
            return BaselineDefenses.fedavg_aggregation(selected_updates)
        else:
            # 单Krum: 选择分数最低的一个客户端
            best_client_idx=np.argmin(scores)
            return client_updates[best_client_idx]
    
    @staticmethod
    def trimmed_mean_aggregation(client_updates: List[Dict[str, torch.Tensor]], 
                                trim_ratio: float=0.2) -> Dict[str, torch.Tensor]:
        """
        Trimmed Mean聚合方法
        参考: Byzantine - Robust Distributed Learning: Towards Optimal Statistical Rates (ICML 2018)
        """
        if not client_updates:
            return {}
        
        n=len(client_updates)
        num_trim=int(n * trim_ratio)
        
        aggregated={}
        for key in client_updates[0].keys():
            param_shape=client_updates[0][key].shape
            
            # 收集所有客户端的该参数
            param_values = torch.stack([update[key] for update in client_updates])
            
            # 对每个参数位置进行trimmed mean
            flattened=param_values.flatten(start_dim = 1)
            
            # 排序并去除极值
            sorted_values, _=torch.sort(flattened, dim=0)
            trimmed_values=sorted_values[num_trim: n - num_trim]
            
            # 计算均值
            trimmed_mean = torch.mean(trimmed_values, dim=0)
            aggregated[key] = trimmed_mean.reshape(param_shape)
        
        return aggregated
    
    @staticmethod
    def median_aggregation(client_updates: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """
        Median聚合方法
        参考: The Hidden Vulnerability of Distributed Learning in Byzantium (ICML 2018)
        """
        if not client_updates:
            return {}
        
        aggregated={}
        for key in client_updates[0].keys():
            param_shape=client_updates[0][key].shape
            
            # 收集所有客户端的该参数
            param_values = torch.stack([update[key] for update in client_updates])
            
            # 计算中位数
            flattened=param_values.flatten(start_dim = 1)
            median_values=torch.median(flattened, dim=0)[0]
            aggregated[key] = median_values.reshape(param_shape)
        
        return aggregated
    
    @staticmethod
    def fedprox_aggregation(client_updates: List[Dict[str, torch.Tensor]], 
                           global_model: Dict[str, torch.Tensor],
                           mu: float=0.01) -> Dict[str, torch.Tensor]:
        """
        FedProx聚合方法（简化版）
        参考: Federated Optimization in Heterogeneous Networks (MLSys 2020)
        注意：完整的FedProx需要在客户端训练时添加正则化项
        """
        if not client_updates:
            return global_model
        
        # 标准FedAvg聚合
        fedavg_result=BaselineDefenses.fedavg_aggregation(client_updates)
        
        # 添加正则化（向全局模型靠拢）
        aggregated={}
        for key in fedavg_result.keys():
            aggregated[key] = (1 - mu) * fedavg_result[key] + mu * global_model[key]
        
        return aggregated
    
    @staticmethod
    def detect_anomalies_statistical(client_updates: List[Dict[str, torch.Tensor]], 
                                   threshold: float=2.0) -> List[bool]:
        """
        基于统计的异常检测
        返回每个客户端是否为异常的布尔列表
        """
        if len(client_updates) < 3:
            return [False] * len(client_updates)
        
        # 计算每个客户端更新的L2范数
        norms=[]
        for update in client_updates:
            total_norm = 0.0
            for param in update.values():
                total_norm += torch.norm(param).item() ** 2
            norms.append(np.sqrt(total_norm))
        
        # 使用Z - score检测异常
        mean_norm=np.mean(norms)
        std_norm=np.std(norms)
        
        anomalies=[]
        for norm in norms:
            z_score = abs(norm - mean_norm) / (std_norm + 1e-8)
            anomalies.append(z_score > threshold)
        
        return anomalies
    
    @staticmethod
    def cosine_similarity_detection(client_updates: List[Dict[str, torch.Tensor]], 
                                  threshold: float=0.5) -> List[bool]:
        """
        基于余弦相似度的异常检测
        """
        if len(client_updates) < 3:
            return [False] * len(client_updates)
        
        # 将参数展平为向量
        client_vectors=[]
        for update in client_updates:
            vector = torch.cat([param.flatten() for param in update.values()])
            client_vectors.append(vector)
        
        # 计算平均向量
        avg_vector=torch.mean(torch.stack(client_vectors), dim=0)
        
        # 计算每个客户端与平均向量的余弦相似度
        anomalies=[]
        for vector in client_vectors:
            similarity = torch.cosine_similarity(vector.unsqueeze(0), avg_vector.unsqueeze(0))
            anomalies.append(similarity.item() < threshold)
        
        return anomalies

class DefenseEvaluator:
    """防御方法评估器"""
    
    def __init__(self):
        self.defense_methods={
            'fedavg': BaselineDefenses.fedavg_aggregation,
            'krum': BaselineDefenses.krum_aggregation,
            'multi_krum': lambda updates: BaselineDefenses.krum_aggregation(updates, multi_krum=True),
            'trimmed_mean': BaselineDefenses.trimmed_mean_aggregation,
            'median': BaselineDefenses.median_aggregation,
            'fedprox': BaselineDefenses.fedprox_aggregation,
        }
    
    def evaluate_defense(self, method_name: str, client_updates: List[Dict[str, torch.Tensor]], 
                        global_model: Dict[str, torch.Tensor] = None, 
                        ground_truth_malicious: List[bool] = None) -> Dict[str, Any]:
        """评估防御方法的效果"""
        
        if method_name not in self.defense_methods:
            raise ValueError(f"未知的防御方法: {method_name}")
        
        # 应用防御方法
        if method_name== 'fedprox' and global_model is not None:
            aggregated = self.defense_methods[method_name](client_updates, global_model)
        else:
            aggregated=self.defense_methods[method_name](client_updates)
        
        # 如果有真实标签，计算检测性能
        detection_metrics={}
        if ground_truth_malicious is not None:
            # 使用统计方法检测异常
            detected_anomalies = BaselineDefenses.detect_anomalies_statistical(client_updates)
            
            # 计算检测指标
            tp=sum(1 for i in range(len(ground_truth_malicious)) 
                    if ground_truth_malicious[i] and detected_anomalies[i])
            fp=sum(1 for i in range(len(ground_truth_malicious)) 
                    if not ground_truth_malicious[i] and detected_anomalies[i])
            tn=sum(1 for i in range(len(ground_truth_malicious)) 
                    if not ground_truth_malicious[i] and not detected_anomalies[i])
            fn=sum(1 for i in range(len(ground_truth_malicious)) 
                    if ground_truth_malicious[i] and not detected_anomalies[i])
            
            precision=tp / (tp + fp) if (tp + fp) > 0 else 0
            recall=tp / (tp + fn) if (tp + fn) > 0 else 0
            f1_score=2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            detection_metrics={
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'true_positive': tp,
                'false_positive': fp,
                'true_negative': tn,
                'false_negative': fn
            }
        
        return {
            'aggregated_model': aggregated,
            'detection_metrics': detection_metrics,
            'method_name': method_name
        }
