#!/usr/bin/env python3
"""
VeryFL - PoL 性能优化器
专为双RTX 4090 + i7 - 13700配置优化
"""

import os
import torch
import psutil
import logging
from typing import Dict, Any

logger=logging.getLogger(__name__)

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.gpu_count=torch.cuda.device_count() if torch.cuda.is_available() else 0
        self.cpu_cores=psutil.cpu_count(logical = False)
        self.cpu_threads=psutil.cpu_count(logical = True)
        self.memory_gb=psutil.virtual_memory().total / (1024**3)
        
    def optimize_pytorch_settings(self):
        """优化PyTorch设置"""
        optimizations=[]
        
        # 1. 优化CPU线程数
        if self.cpu_cores >= 8:
            # 高端CPU：为每个并行实验分配合理的线程数
            threads_per_experiment = max(2, self.cpu_threads // 6)  # 为6个并行实验预留
            torch.set_num_threads(threads_per_experiment)
            os.environ['OMP_NUM_THREADS'] = str(threads_per_experiment)
            os.environ['MKL_NUM_THREADS'] = str(threads_per_experiment)
            optimizations.append(f"CPU线程优化: {threads_per_experiment} 线程/实验")
        
        # 2. 优化CUDA设置
        if self.gpu_count > 0:
            # 启用CUDA优化
            torch.backends.cudnn.benchmark=True  # 自动寻找最优算法
            torch.backends.cudnn.deterministic = False  # 允许非确定性以提升性能
            os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # 异步CUDA调用
            optimizations.append("CUDA优化: 启用cudnn.benchmark")
            
            # 多GPU优化
            if self.gpu_count >= 2:
                os.environ['CUDA_DEVICE_ORDER'] = 'PCI_BUS_ID'  # 稳定的GPU顺序
                optimizations.append(f"多GPU优化: {self.gpu_count} GPU负载均衡")
        
        # 3. 内存优化
        if self.memory_gb >= 32:
            # 大内存：启用内存映射和预分配
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb: 512'
            optimizations.append("内存优化: 大内存配置")
        
        # 4. 数据加载优化
        if self.cpu_cores >= 8:
            # 多核CPU：增加数据加载worker数量
            num_workers=min(self.cpu_cores // 2, 8)
            os.environ['DATALOADER_NUM_WORKERS'] = str(num_workers)
            optimizations.append(f"数据加载优化: {num_workers} workers")
        
        return optimizations
    
    def get_recommended_config(self) -> Dict[str, Any]:
        """获取推荐配置"""
        config={
            'hardware_info': {
                'gpu_count': self.gpu_count,
                'cpu_cores': self.cpu_cores,
                'cpu_threads': self.cpu_threads,
                'memory_gb': self.memory_gb
            }
        }
        
        # 推荐并行度
        if self.gpu_count >= 2 and self.memory_gb >= 32:
            # 双GPU高端配置
            config['recommended_parallel'] = min(self.gpu_count * 3, 8)
            config['performance_tier'] = 'high_end_multi_gpu'
        elif self.gpu_count== 1 and self.memory_gb >= 16:
            # 单GPU配置
            config['recommended_parallel'] = 3
            config['performance_tier'] = 'single_gpu'
        else:
            # CPU配置
            config['recommended_parallel'] = min(self.cpu_cores // 2, 4)
            config['performance_tier'] = 'cpu_only'
        
        # 推荐实验配置
        config['experiment_settings'] = {
            'batch_size': 64 if self.gpu_count > 0 else 32,
            'num_workers': min(self.cpu_cores // 2, 8) if self.cpu_cores >= 4 else 2,
            'pin_memory': self.gpu_count > 0,
            'persistent_workers': True
        }
        
        return config
    
    def apply_optimizations(self):
        """应用所有优化"""
        print("🚀 应用性能优化...")
        
        # 应用PyTorch优化
        optimizations=self.optimize_pytorch_settings()
        
        # 显示优化结果
        print("✅ 性能优化完成:")
        for opt in optimizations:
            print(f"   - {opt}")
        
        # 显示硬件信息
        config=self.get_recommended_config()
        print(f"\n🖥️ 硬件配置:")
        print(f"   - GPU: {self.gpu_count} x RTX 4090 (24GB each)")
        print(f"   - CPU: {self.cpu_cores} 核心 / {self.cpu_threads} 线程")
        print(f"   - 内存: {self.memory_gb:.1f}GB")
        print(f"   - 性能等级: {config['performance_tier']}")
        print(f"   - 推荐并行度: {config['recommended_parallel']}")
        
        return config

# 全局优化器实例
performance_optimizer=PerformanceOptimizer()

def optimize_for_hardware():
    """为当前硬件优化性能"""
    return performance_optimizer.apply_optimizations()

def get_optimal_settings():
    """获取最优设置"""
    return performance_optimizer.get_recommended_config()

if __name__== "__main__":
    # 直接运行时显示优化信息
    optimize_for_hardware()
