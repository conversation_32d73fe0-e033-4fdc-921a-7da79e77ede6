"""
区块链交互模块
提供客户端与区块链的交互接口
"""
from util import jsonFormat
from collections import defaultdict
import string
import json
import logging
import os
import torch

logger=logging.getLogger(__name__)

# 使用连接管理器初始化区块链
try:
    from .connection_manager import connection_manager

    # 尝试加载项目 - 处理并发加载问题
    current_dir=os.path.dirname(os.path.abspath(__file__))
    project_path=os.path.join(current_dir, "..", "chainEnv")

    from brownie import project
    import time
    import random

    # 处理并发项目加载
    max_retries=3
    p = None
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                delay=random.uniform(0.1, 0.3)
                time.sleep(delay)

            p=project.load(project_path = project_path)
            p.load_config()
            break

        except Exception as e:
            if "already a project loaded with this name" in str(e):
                # 项目已经被其他进程加载，这是正常的并发情况
                logger.info("📋 项目已被其他进程加载，继续使用现有配置")
                # 直接使用brownie的全局配置，不需要特定的项目实例
                p=None  # 标记为使用全局配置
                break
            elif attempt == max_retries - 1:
                raise
            else:
                logger.info(f"⏳ 项目加载重试中... (尝试 {attempt + 1}/{max_retries})")

    # 导入合约 - 处理项目加载情况
    if p:
        # 使用加载的项目
        PoLManager=p.PoLManager
        SimplePoLManager = p.SimplePoLManager
        NetworkManager = p.NetworkManager
        clientManager = p.clientManager
        watermarkNegotiation = p.watermarkNegotiation
    else:
        # 项目已被其他进程加载，使用全局合约引用
        # 这种情况下，合约应该已经在全局命名空间中可用
        try:
            from brownie import Contract
            # 尝试通过Contract接口访问已部署的合约
            # 这是一个备用方案，主要依赖连接管理器
            PoLManager = None
            SimplePoLManager = None
            NetworkManager = None
            clientManager = None
            watermarkNegotiation = None
            logger.info("📋 使用连接管理器提供的合约实例")
        except Exception as e:
            logger.warning(f"⚠️ 合约导入警告: {e}")
            # 设置为None，依赖连接管理器
            PoLManager=SimplePoLManager = NetworkManager = clientManager = watermarkNegotiation = None

    # 加载Token合约（用于模型版权保护和交易）
    try:
        # 尝试从token子项目加载
        token_project_path = os.path.join(project_path, "token")
        if os.path.exists(token_project_path):
            token_p=project.load(project_path = token_project_path)
            Token=token_p.Token
            # SafeMath不再需要，Solidity 0.8+内置溢出检查
            SafeMath = None
            logger.info("✅ 成功加载Token合约")
        else:
            logger.warning("⚠️ Token目录不存在，模型交易功能可能受限")
            Token=None
            SafeMath = None
    except Exception as e:
        logger.warning(f"⚠️ Token合约加载失败: {e}")
        Token=None
        SafeMath = None

    logger.info(f"✅ 成功加载合约: {list(p.__all__)}")
    
    # 使用连接管理器获取连接
    network=connection_manager.get_connection("interact_module")
    accounts=connection_manager.accounts
    server_accounts = connection_manager.get_account(0)
    
    # 获取合约实例
    watermarkNegotiation=connection_manager.get_contract('watermarkNegotiation')
    clientManager=connection_manager.get_contract('clientManager')
    
    logger.info("✅ 区块链连接成功")
        
except Exception as e:
    logger.error(f"❌ 区块链初始化失败: {e}")
    logger.error("请确保:")
    logger.error("1. Ganache正在运行: ./start_ganache.sh")
    logger.error("2. 智能合约已编译: cd chainEnv && brownie compile")
    logger.error("3. 网络配置正确")
    raise RuntimeError(f"区块链初始化失败: {e}")

def upload():
    """上传数据到区块链"""
    raise NotImplementedError("上传功能待实现")

class chainProxy:
    """区块链代理类"""
    
    def __init__(self):
        self.watermark_contract=watermarkNegotiation
        self.client_contract = clientManager
        self.server_account = server_accounts
    
    def register_client(self, client_address: str) -> bool:
        """注册客户端"""
        try:
            tx=self.client_contract.registerClient(client_address, {'from': self.server_account})
            return tx.status== 1
        except Exception as e:
            logger.error(f"客户端注册失败: {e}")
            return False
    
    def submit_model_update(self, client_address: str, model_hash: str, pol_proof: str) -> bool:
        """提交模型更新"""
        try:
            tx=self.client_contract.submitModelUpdate(
                client_address, model_hash, pol_proof, 
                {'from': self.server_account}
            )
            return tx.status== 1
        except Exception as e:
            logger.error(f"模型更新提交失败: {e}")
            return False
    
    def verify_pol_proof(self, client_address: str, pol_proof: str) -> bool:
        """验证PoL证明"""
        try:
            # 调用智能合约验证
            result=self.client_contract.verifyPoLProof(client_address, pol_proof)
            return result
        except Exception as e:
            logger.error(f"PoL证明验证失败: {e}")
            return False
    
    def get_client_reputation(self, client_address: str) -> int:
        """获取客户端信誉"""
        try:
            reputation=self.client_contract.getClientReputation(client_address)
            return reputation
        except Exception as e:
            logger.error(f"获取客户端信誉失败: {e}")
            return 0
    
    def distribute_incentives(self, client_address: str, amount: int) -> bool:
        """分发激励"""
        try:
            tx=self.client_contract.distributeIncentive(
                client_address, amount,
                {'from': self.server_account}
            )
            return tx.status== 1
        except Exception as e:
            logger.error(f"激励分发失败: {e}")
            return False

    # 添加task.py中需要的方法
    def client_regist(self):
        """客户端注册（兼容旧接口）"""
        # 为了兼容性，这里简单返回True
        # 实际的客户端注册逻辑可能需要地址参数
        logger.info("客户端注册请求")
        return True

    def get_client_num(self):
        """获取客户端数量"""
        # 返回默认客户端数量，实际应该从区块链获取
        return 10

    def get_client_list(self):
        """获取客户端列表"""
        # 从区块链获取客户端列表
        client_list={}
        for i in range(self.get_client_num()):
            client_list[f"client_{i}"] = f"0x{i: 040x}"
        return client_list

    def construct_sign(self, global_args):
        """构造签名（兼容旧接口）"""
        # 返回签名配置
        return {
            'bit_length': global_args.get('bit_length', 40),
            'sign_config': global_args.get('sign_config', {}),
            'sign_num': global_args.get('sign_num', 10)
        }

# 创建全局代理实例
chain_proxy=chainProxy()
