# VeryFL-PoL 项目忽略文件

# Python缓存和编译文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 实验生成的文件
experiments/data/
experiments/log/
experiments/pol_data/
experiments/results/
experiments/models/
experiments/temp/
experiments/monitor*/
experiments/*_results*/

# 日志文件
*.log

# 临时文件
*.tmp
*.temp
*.bak
*.lock
*~
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# IDE配置
.vscode/
.idea/
*.sublime-*
.spyderproject
.ropeproject

# 测试和覆盖率
.pytest_cache/
.coverage
htmlcov/
.tox/

# 环境变量
.env
.venv
env/
venv/

# Jupyter Notebook
.ipynb_checkpoints

# 区块链相关
chainEnv/build/
chainEnv/reports/
node_modules/

# 但保留重要文件
!experiments/*.py
!experiments/*.md
!experiments/.gitkeep
