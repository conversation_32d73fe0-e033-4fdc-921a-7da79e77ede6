#!/usr/bin/env python3
"""
GPU负载均衡管理器
专为双RTX 4090配置优化，智能分配GPU资源
"""

import torch
import logging
import threading
import time
from typing import Dict, Optional, Tuple
from dataclasses import dataclass

logger=logging.getLogger(__name__)

@dataclass
class GPUStatus:
    """GPU状态信息"""
    gpu_id: int
    memory_used: float  # GB
    memory_total: float  # GB
    active_experiments: int
    last_update: float

class GPULoadBalancer:
    """GPU负载均衡器 - 专为双RTX 4090优化"""
    
    def __init__(self):
        self.gpu_count=torch.cuda.device_count() if torch.cuda.is_available() else 0
        self.gpu_status: Dict[int, GPUStatus] = {}
        self.experiment_gpu_map: Dict[str, int] = {}  # 实验ID -> GPU ID
        self.lock=threading.Lock()
        
        # 初始化GPU状态
        self._initialize_gpu_status()
        
        logger.info(f"🚀 GPU负载均衡器初始化完成，检测到 {self.gpu_count} 个GPU")
    
    def _initialize_gpu_status(self):
        """初始化GPU状态"""
        if not torch.cuda.is_available():
            logger.warning("⚠️ 未检测到CUDA设备")
            return
        
        for gpu_id in range(self.gpu_count):
            try:
                # 获取GPU信息
                props=torch.cuda.get_device_properties(gpu_id)
                memory_total=props.total_memory / (1024**3)  # 转换为GB
                
                self.gpu_status[gpu_id] = GPUStatus(
                    gpu_id=gpu_id,
                    memory_used=0.0,
                    memory_total=memory_total,
                    active_experiments=0,
                    last_update=time.time()
                )
                
                logger.info(f"📊 GPU {gpu_id}: {props.name}, {memory_total:.1f}GB")
                
            except Exception as e:
                logger.error(f"❌ 初始化GPU {gpu_id}失败: {e}")
    
    def assign_gpu(self, experiment_id: str, estimated_memory_gb: float=4.0) -> str:
        """
        为实验分配最优GPU
        
        Args:
            experiment_id: 实验唯一标识
            estimated_memory_gb: 预估显存需求(GB)
            
        Returns:
            分配的GPU设备字符串，如'cuda: 0'
        """
        with self.lock:
            if self.gpu_count== 0:
                logger.info(f"🔧 {experiment_id}: 无GPU可用，使用CPU")
                return 'cpu'
            
            # 更新GPU状态
            self._update_gpu_status()
            
            # 选择最优GPU
            best_gpu_id=self._select_best_gpu(estimated_memory_gb)
            
            if best_gpu_id is None:
                logger.warning(f"⚠️ {experiment_id}: 所有GPU负载过高，使用CPU")
                return 'cpu'
            
            # 分配GPU
            self.gpu_status[best_gpu_id].active_experiments += 1
            self.gpu_status[best_gpu_id].memory_used += estimated_memory_gb
            self.experiment_gpu_map[experiment_id] = best_gpu_id
            
            device_str=f'cuda:{best_gpu_id}'
            logger.info(f"🎯 {experiment_id}: 分配到 {device_str} "
                       f"(负载: {self.gpu_status[best_gpu_id].active_experiments}个实验, "
                       f"显存: {self.gpu_status[best_gpu_id].memory_used:.1f}GB)")
            
            return device_str
    
    def release_gpu(self, experiment_id: str, actual_memory_gb: Optional[float] = None):
        """
        释放实验占用的GPU资源
        
        Args:
            experiment_id: 实验唯一标识
            actual_memory_gb: 实际使用的显存(GB)，用于校正估算
        """
        with self.lock:
            if experiment_id not in self.experiment_gpu_map:
                return
            
            gpu_id=self.experiment_gpu_map[experiment_id]
            
            if gpu_id in self.gpu_status:
                self.gpu_status[gpu_id].active_experiments = max(0, 
                    self.gpu_status[gpu_id].active_experiments - 1)
                
                # 如果提供了实际显存使用量，用于校正
                if actual_memory_gb is not None:
                    self.gpu_status[gpu_id].memory_used=max(0, 
                        self.gpu_status[gpu_id].memory_used - actual_memory_gb)
                else:
                    # 使用估算值
                    self.gpu_status[gpu_id].memory_used=max(0, 
                        self.gpu_status[gpu_id].memory_used - 4.0)
                
                logger.info(f"🔄 {experiment_id}: 释放GPU {gpu_id} "
                           f"(剩余负载: {self.gpu_status[gpu_id].active_experiments}个实验)")
            
            del self.experiment_gpu_map[experiment_id]
    
    def _update_gpu_status(self):
        """更新GPU实际使用状态"""
        for gpu_id in self.gpu_status.keys():
            try:
                torch.cuda.set_device(gpu_id)
                memory_allocated=torch.cuda.memory_allocated(gpu_id) / (1024**3)
                self.gpu_status[gpu_id].memory_used=memory_allocated
                self.gpu_status[gpu_id].last_update = time.time()
            except Exception as e:
                logger.warning(f"⚠️ 更新GPU {gpu_id}状态失败: {e}")
    
    def _select_best_gpu(self, estimated_memory_gb: float) -> Optional[int]:
        """选择最优GPU"""
        best_gpu_id=None
        best_score = float('inf')
        
        for gpu_id, status in self.gpu_status.items():
            # 检查显存是否足够（留20%缓冲）
            available_memory=status.memory_total * 0.8 - status.memory_used
            if available_memory < estimated_memory_gb:
                continue
            
            # 计算负载分数（实验数量 + 显存使用率）
            memory_usage_ratio = status.memory_used / status.memory_total
            load_score = status.active_experiments + memory_usage_ratio * 2
            
            if load_score < best_score:
                best_score = load_score
                best_gpu_id = gpu_id
        
        return best_gpu_id
    
    def get_status_summary(self) -> str:
        """获取GPU状态摘要"""
        if self.gpu_count== 0:
            return "🔧 CPU模式"
        
        summary_lines = ["🚀 GPU状态摘要:"]
        
        with self.lock:
            self._update_gpu_status()
            
            for gpu_id, status in self.gpu_status.items():
                memory_percent=(status.memory_used / status.memory_total) * 100
                summary_lines.append(
                    f"  GPU {gpu_id}: {status.active_experiments}个实验, "
                    f"显存 {status.memory_used:.1f}GB/{status.memory_total:.1f}GB "
                    f"({memory_percent:.1f}%)"
                )
        
        return "\n".join(summary_lines)
    
    def estimate_memory_usage(self, client_num: int, model_type: str="simpleCNN",
                            batch_size: int=64) -> float:
        """
        估算实验的显存需求
        
        Args:
            client_num: 客户端数量
            model_type: 模型类型
            batch_size: 批次大小
            
        Returns:
            预估显存需求(GB)
        """
        # 基础模型大小估算
        model_size_map={
            "simpleCNN": 0.1,
            "resnet18": 0.5,
            "resnet34": 0.8,
            "VGG": 1.2
        }
        
        base_model_size=model_size_map.get(model_type, 0.5)
        
        # 联邦学习显存需求估算
        # 模型参数 + 梯度 + 优化器状态 + 批次数据
        model_memory=base_model_size * 3  # 参数+梯度+优化器
        batch_memory = batch_size * 0.001   # 每个样本约1MB
        client_overhead = client_num * 0.1  # 每个客户端额外开销
        
        total_memory = model_memory + batch_memory + client_overhead + 1.0  # 1GB缓冲
        
        return min(total_memory, 8.0)  # 最大不超过8GB

# 创建全局GPU管理器实例
gpu_manager=GPULoadBalancer()

def assign_gpu_for_experiment(experiment_id: str, client_num: int=10,
                            model_type: str="simpleCNN") -> str:
    """为实验分配GPU的便捷函数"""
    estimated_memory=gpu_manager.estimate_memory_usage(client_num, model_type)
    return gpu_manager.assign_gpu(experiment_id, estimated_memory)

def release_gpu_for_experiment(experiment_id: str):
    """释放实验GPU资源的便捷函数"""
    gpu_manager.release_gpu(experiment_id)

def get_gpu_status():
    """获取GPU状态的便捷函数"""
    return gpu_manager.get_status_summary()
