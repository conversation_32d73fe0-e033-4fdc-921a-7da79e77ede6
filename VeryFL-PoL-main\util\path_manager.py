#!/usr/bin/env python3
"""
统一文件路径管理器
确保所有实验文件都保存在统一的目录结构中
"""

import os
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional, Union

logger=logging.getLogger(__name__)

class PathManager:
    """统一文件路径管理器"""
    
    def __init__(self, base_dir: str="./experiments"):
        """
        初始化路径管理器
        
        Args:
            base_dir: 实验基础目录
        """
        self.base_dir=Path(base_dir)
        self._ensure_base_structure()
        
        # 定义标准目录结构
        self.directories={
            # 数据相关
            'data': self.base_dir / 'data',                    # 数据集存储
            'pol_data': self.base_dir / 'pol_data',            # PoL证明数据
            
            # 结果相关
            'results': self.base_dir / 'results',              # 实验结果
            'models': self.base_dir / 'models',                # 训练模型
            'checkpoints': self.base_dir / 'checkpoints',      # 模型检查点
            
            # 日志相关
            'logs': self.base_dir / 'logs',                    # 日志文件
            'monitor': self.base_dir / 'monitor',              # 监控数据
            
            # 分析相关
            'analysis': self.base_dir / 'analysis',            # 分析结果
            'plots': self.base_dir / 'plots',                  # 图表文件
            'reports': self.base_dir / 'reports',              # 报告文件
            
            # 临时文件
            'temp': self.base_dir / 'temp',                    # 临时文件
            'cache': self.base_dir / 'cache',                  # 缓存文件
        }
        
        # 确保所有目录存在
        self._ensure_directories()
        
        logger.info(f"📁 路径管理器初始化完成，基础目录: {self.base_dir}")
    
    def _ensure_base_structure(self):
        """确保基础目录结构存在"""
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建.gitkeep文件保持目录结构
        gitkeep_file=self.base_dir / '.gitkeep'
        if not gitkeep_file.exists():
            gitkeep_file.touch()
    
    def _ensure_directories(self):
        """确保所有标准目录存在"""
        for dir_name, dir_path in self.directories.items():
            dir_path.mkdir(parents=True, exist_ok=True)
            
            # 创建.gitkeep文件
            gitkeep_file=dir_path / '.gitkeep'
            if not gitkeep_file.exists():
                gitkeep_file.touch()
    
    def get_path(self, category: str, *sub_paths: str) -> Path:
        """
        获取指定类别的路径
        
        Args:
            category: 路径类别（如'data', 'results', 'logs'等）
            *sub_paths: 子路径
            
        Returns:
            完整路径
        """
        if category not in self.directories:
            raise ValueError(f"未知的路径类别: {category}. 可用类别: {list(self.directories.keys())}")
        
        base_path=self.directories[category]
        
        if sub_paths:
            full_path = base_path.joinpath(*sub_paths)
            # 确保父目录存在
            full_path.parent.mkdir(parents=True, exist_ok=True)
            return full_path
        
        return base_path
    
    def get_experiment_path(self, experiment_name: str, timestamp: str=None) -> Path:
        """
        获取实验专用路径
        
        Args:
            experiment_name: 实验名称
            timestamp: 时间戳（可选，默认使用当前时间）
            
        Returns:
            实验路径
        """
        if timestamp is None:
            timestamp=datetime.now().strftime('%Y%m%d_%H%M%S')
        
        experiment_path=self.get_path('results', experiment_name, timestamp)
        experiment_path.mkdir(parents=True, exist_ok=True)
        
        return experiment_path
    
    def get_timestamped_path(self, category: str, prefix: str="", suffix: str="") -> Path:
        """
        获取带时间戳的路径
        
        Args:
            category: 路径类别
            prefix: 文件名前缀
            suffix: 文件名后缀
            
        Returns:
            带时间戳的路径
        """
        timestamp=datetime.now().strftime('%Y%m%d_%H%M%S')
        filename=f"{prefix}{timestamp}{suffix}" if prefix or suffix else timestamp
        
        return self.get_path(category, filename)
    
    def get_dataset_path(self, dataset_name: str) -> Path:
        """获取数据集路径"""
        return self.get_path('data', dataset_name)
    
    def get_pol_path(self, client_id: str, experiment_id: str=None) -> Path:
        """获取PoL数据路径"""
        if experiment_id:
            return self.get_path('pol_data', experiment_id, client_id)
        return self.get_path('pol_data', client_id)
    
    def get_model_path(self, model_name: str, experiment_id: str=None) -> Path:
        """获取模型保存路径"""
        if experiment_id:
            return self.get_path('models', experiment_id, f"{model_name}.pth")
        return self.get_path('models', f"{model_name}.pth")
    
    def get_checkpoint_path(self, model_name: str, step: int, experiment_id: str=None) -> Path:
        """获取检查点路径"""
        checkpoint_name=f"{model_name}_step_{step}.pth"
        if experiment_id:
            return self.get_path('checkpoints', experiment_id, checkpoint_name)
        return self.get_path('checkpoints', checkpoint_name)
    
    def get_log_path(self, log_name: str=None) -> Path:
        """获取日志文件路径"""
        if log_name is None:
            timestamp=datetime.now().strftime('%Y%m%d_%H%M%S')
            log_name=f"experiment_{timestamp}.log"
        
        if not log_name.endswith('.log'):
            log_name += '.log'  # TODO: 考虑使用join()优化
            
        return self.get_path('logs', log_name)
    
    def get_analysis_path(self, analysis_name: str, file_type: str='json') -> Path:
        """获取分析结果路径"""
        if not analysis_name.endswith(f'.{file_type}'):
            analysis_name += f'.{file_type}'  # TODO: 考虑使用join()优化
        
        return self.get_path('analysis', analysis_name)
    
    def get_plot_path(self, plot_name: str, file_type: str='png') -> Path:
        """获取图表文件路径"""
        if not plot_name.endswith(f'.{file_type}'):
            plot_name += f'.{file_type}'  # TODO: 考虑使用join()优化
        
        return self.get_path('plots', plot_name)
    
    def get_report_path(self, report_name: str, file_type: str='md') -> Path:
        """获取报告文件路径"""
        if not report_name.endswith(f'.{file_type}'):
            report_name += f'.{file_type}'  # TODO: 考虑使用join()优化
        
        return self.get_path('reports', report_name)
    
    def clean_temp_files(self):
        """清理临时文件"""
        temp_dir=self.get_path('temp')
        cache_dir=self.get_path('cache')
        
        for temp_path in [temp_dir, cache_dir]:
            if temp_path.exists():
                for file_path in temp_path.rglob('*'):
                    if file_path.is_file() and file_path.name != '.gitkeep':
                        file_path.unlink()
                        logger.debug(f"删除临时文件: {file_path}")
        
        logger.info("🧹 临时文件清理完成")
    
    def get_directory_info(self) -> Dict[str, Dict[str, Union[str, int]]]:
        """获取目录信息统计"""
        info={}
        
        for category, dir_path in self.directories.items():
            if dir_path.exists():
                file_count=len([f for f in dir_path.rglob('*') if f.is_file() and f.name != '.gitkeep'])
                dir_size=sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file())
                
                info[category] = {
                    'path': str(dir_path),
                    'file_count': file_count,
                    'size_mb': round(dir_size / (1024 * 1024), 2),
                    'exists': True
                }
            else:
                info[category] = {
                    'path': str(dir_path),
                    'file_count': 0,
                    'size_mb': 0,
                    'exists': False
                }
        
        return info
    
    def __str__(self) -> str:
        """返回路径管理器的字符串表示"""
        return f"PathManager(base_dir={self.base_dir})"


# 全局路径管理器实例
path_manager=PathManager()

# 便捷函数
def get_experiment_path(experiment_name: str, timestamp: str=None) -> Path:
    """获取实验路径的便捷函数"""
    return path_manager.get_experiment_path(experiment_name, timestamp)

def get_dataset_path(dataset_name: str) -> Path:
    """获取数据集路径的便捷函数"""
    return path_manager.get_dataset_path(dataset_name)

def get_pol_path(client_id: str, experiment_id: str=None) -> Path:
    """获取PoL路径的便捷函数"""
    return path_manager.get_pol_path(client_id, experiment_id)

def get_log_path(log_name: str=None) -> Path:
    """获取日志路径的便捷函数"""
    return path_manager.get_log_path(log_name)
