# VeryFL-PoL 统一配置文件
# 简化所有实验配置，提供默认值和常用组合

# 基础配置
datasets:
  - FashionMNIST
  - CIFAR10
  - CIFAR100

models:
  - simpleCNN  # 修正模型名称，ModelFactory期望小写
  - resnet18
  - resnet34

# 实验规模配置
client_nums:
  - 5    # 快速测试
  - 10   # 标准配置
  - 20   # 大规模测试

communication_rounds:
  - 2    # 快速测试
  - 5    # 标准配置
  - 10   # 完整实验

# 攻击实验配置
attack_types:
  - free_rider
  - model_poison
  - data_poison

malicious_ratios:
  - 0.1  # 10%恶意客户端
  - 0.2  # 20%恶意客户端
  - 0.3  # 30%恶意客户端

# 压缩方法配置
compression_methods:
  - none
  - quantization
  - incremental

# 网络条件配置
network_conditions:
  - ideal
  - high_latency
  - low_bandwidth
  - unstable

# 预定义实验套件
experiment_suites:
  # 快速测试套件
  quick:
    - name: "pol_quick_test"
      dataset: "FashionMNIST"
      rounds: 2
      client_num: 5
      enable_pol: true
    
    - name: "baseline_quick_test"
      dataset: "FashionMNIST"
      rounds: 2
      client_num: 5
      enable_pol: false

  # 性能对比套件
  performance:
    - name: "pol_fashion_mnist"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      enable_pol: true
    
    - name: "baseline_fashion_mnist"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      enable_pol: false
    
    - name: "pol_cifar10"
      dataset: "CIFAR10"
      rounds: 5
      client_num: 10
      enable_pol: true
    
    - name: "baseline_cifar10"
      dataset: "CIFAR10"
      rounds: 5
      client_num: 10
      enable_pol: false

  # 攻击防御套件
  attack_defense:
    - name: "free_rider_attack"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      attack_type: "free_rider"
      malicious_ratio: 0.2
      enable_pol: true
    
    - name: "model_poison_attack"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      attack_type: "model_poison"
      malicious_ratio: 0.1
      enable_pol: true
    
    - name: "data_poison_attack"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      attack_type: "data_poison"
      malicious_ratio: 0.1
      enable_pol: true

  # 压缩效果套件
  compression:
    - name: "no_compression"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      compression_method: "none"
      enable_pol: true
    
    - name: "quantization_compression"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      compression_method: "quantization"
      enable_pol: true
    
    - name: "incremental_compression"
      dataset: "FashionMNIST"
      rounds: 5
      client_num: 10
      compression_method: "incremental"
      enable_pol: true

# GPU配置
gpu:
  # 自动GPU分配
  auto_assign: true
  
  # 显存估算配置
  memory_estimation:
    simpleCNN: 2.0    # GB
    resnet18: 4.0     # GB
    resnet34: 6.0     # GB
    VGG: 8.0          # GB
  
  # 并行度配置
  max_parallel:
    cpu: 4
    single_gpu: 2
    dual_gpu: 4

# 输出配置
output:
  results_dir: "results"
  log_level: "INFO"
  save_models: false
  save_checkpoints: false
  
  # 结果格式
  formats:
    - json
    - csv

# PoL配置
pol:
  # 默认PoL参数
  save_freq: null  # 自动设置
  verification_ratio: 0.2
  enable_compression: true
  enable_blockchain: true
  enable_incentives: true
  
  # 验证器配置
  verifier:
    enable_multi_metric: true
    enable_gpu_acceleration: true
    enable_parallel_verification: true
    max_parallel_workers: 4

# 区块链配置
blockchain:
  auto_start: true
  network: "ganache"
  gas_price: "100000000000"  # 100 gwei
  gas_limit: "6721975"

# 实验监控配置
monitoring:
  enable: true
  metrics:
    - accuracy
    - loss
    - communication_cost
    - storage_cost
    - training_time
    - detection_rate
  
  # 实时监控
  realtime: false
  update_interval: 10  # 秒

# 高级配置
advanced:
  # 容错配置
  retry_failed: true
  max_retries: 3
  timeout: 1800  # 30分钟
  
  # 资源管理
  memory_limit: "16GB"
  cpu_limit: 8
  
  # 调试配置
  debug_mode: false
  verbose_logging: false
  save_intermediate_results: false
