#!/usr/bin/env python3
"""
路径配置验证脚本
验证所有文件路径配置是否正确使用统一路径管理器
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from util.path_manager import path_manager

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger=logging.getLogger(__name__)

def verify_path_configuration():
    """验证路径配置"""
    logger.info("🔍 开始验证路径配置...")
    logger.info("=" * 60)
    
    # 1. 验证路径管理器初始化
    logger.info("📁 验证路径管理器初始化...")
    try:
        info=path_manager.get_directory_info()
        logger.info(f"   ✅ 路径管理器初始化成功，基础目录: {path_manager.base_dir}")
        logger.info(f"   📊 管理 {len(info)} 个标准目录")
    except Exception as e:
        logger.error(f"   ❌ 路径管理器初始化失败: {e}")
        return False
    
    # 2. 验证目录结构
    logger.info("\n📂 验证目录结构...")
    all_dirs_exist=True
    for category, dir_info in info.items():
        if dir_info['exists']:
            logger.info(f"   ✅ {category}: {dir_info['path']} "
                       f"({dir_info['file_count']} 文件, {dir_info['size_mb']} MB)")
        else:
            logger.warning(f"   ⚠️ {category}: {dir_info['path']} (不存在)")
            all_dirs_exist=False
    
    # 3. 验证配置文件路径
    logger.info("\n⚙️ 验证配置文件路径...")
    try:
        from config.dataset import dataset_file_path
        from config.log import log_folder
        
        expected_data_path=str(path_manager.get_path('data'))
        expected_log_path=str(path_manager.get_path('logs'))
        
        if dataset_file_path== expected_data_path:
            logger.info(f"   ✅ 数据集路径配置正确: {dataset_file_path}")
        else:
            logger.error(f"   ❌ 数据集路径配置错误: {dataset_file_path} != {expected_data_path}")
            return False
        
        if log_folder== expected_log_path:
            logger.info(f"   ✅ 日志路径配置正确: {log_folder}")
        else:
            logger.error(f"   ❌ 日志路径配置错误: {log_folder} != {expected_log_path}")
            return False
            
    except Exception as e:
        logger.error(f"   ❌ 配置文件路径验证失败: {e}")
        return False
    
    # 4. 验证PoL路径配置
    logger.info("\n🔒 验证PoL路径配置...")
    try:
        from pol.pol_generator import PoLGenerator
        
        # 测试默认路径
        pol_gen=PoLGenerator()
        expected_pol_path=str(path_manager.get_path('pol_data'))
        
        if pol_gen.save_dir== expected_pol_path:
            logger.info(f"   ✅ PoL路径配置正确: {pol_gen.save_dir}")
        else:
            logger.error(f"   ❌ PoL路径配置错误: {pol_gen.save_dir} != {expected_pol_path}")
            return False
            
    except Exception as e:
        logger.error(f"   ❌ PoL路径配置验证失败: {e}")
        return False
    
    # 5. 验证实验监控路径
    logger.info("\n📊 验证实验监控路径...")
    try:
        from experiments.experiment_monitor import ExperimentMonitor
        
        monitor=ExperimentMonitor()
        monitor_base=str(path_manager.get_path('monitor'))
        
        if monitor.output_dir.startswith(monitor_base):
            logger.info(f"   ✅ 监控路径配置正确: {monitor.output_dir}")
        else:
            logger.error(f"   ❌ 监控路径配置错误: {monitor.output_dir} 不在 {monitor_base} 下")
            return False
            
    except Exception as e:
        logger.error(f"   ❌ 监控路径配置验证失败: {e}")
        return False
    
    # 6. 测试路径生成功能
    logger.info("\n🧪 测试路径生成功能...")
    try:
        # 测试实验路径
        exp_path=path_manager.get_experiment_path("test_experiment")
        logger.info(f"   ✅ 实验路径生成: {exp_path}")
        
        # 测试时间戳路径
        ts_path=path_manager.get_timestamped_path('logs', 'test_', '.log')
        logger.info(f"   ✅ 时间戳路径生成: {ts_path}")
        
        # 测试模型路径
        model_path=path_manager.get_model_path("test_model", "test_exp")
        logger.info(f"   ✅ 模型路径生成: {model_path}")
        
        # 测试检查点路径
        checkpoint_path=path_manager.get_checkpoint_path("test_model", 100, "test_exp")
        logger.info(f"   ✅ 检查点路径生成: {checkpoint_path}")
        
    except Exception as e:
        logger.error(f"   ❌ 路径生成功能测试失败: {e}")
        return False
    
    # 7. 验证与现有实验框架的兼容性
    logger.info("\n🔗 验证与实验框架的兼容性...")
    try:
        from analyze_results import ExperimentAnalyzer
        
        analyzer=ExperimentAnalyzer()
        results_base=str(path_manager.get_path('results'))
        
        if analyzer.results_dir.startswith(results_base):
            logger.info(f"   ✅ 结果分析器路径兼容: {analyzer.results_dir}")
        else:
            logger.warning(f"   ⚠️ 结果分析器路径可能不兼容: {analyzer.results_dir}")
            
    except Exception as e:
        logger.warning(f"   ⚠️ 实验框架兼容性验证失败: {e}")
    
    logger.info("\n" + "=" * 60)
    if all_dirs_exist:
        logger.info("✅ 路径配置验证完成，所有配置正确！")
        return True
    else:
        logger.warning("⚠️ 路径配置验证完成，但存在一些问题")
        return True  # 不存在的目录不是致命错误

def show_path_structure():
    """显示路径结构"""
    logger.info("\n📁 当前路径结构:")
    logger.info("=" * 40)
    
    info=path_manager.get_directory_info()
    for category, dir_info in info.items():
        status="✅" if dir_info['exists'] else "📁"
        logger.info(f"{status} {category: 12} -> {dir_info['path']}")
        if dir_info['exists'] and dir_info['file_count'] > 0:
            logger.info(f"   📊 {dir_info['file_count']} 文件, {dir_info['size_mb']} MB")

def clean_empty_directories():
    """清理空目录（保留.gitkeep）"""
    logger.info("\n🧹 清理空目录...")
    
    cleaned_count=0
    for category, dir_path in path_manager.directories.items():
        if dir_path.exists():
            files=[f for f in dir_path.rglob('*') if f.is_file() and f.name != '.gitkeep']
            if not files:
                # 只有.gitkeep文件或完全空的目录
                for file_path in dir_path.rglob('*'):
                    if file_path.is_file() and file_path.name != '.gitkeep':
                        file_path.unlink()
                        cleaned_count += 1
    
    if cleaned_count > 0:
        logger.info(f"   🧹 清理了 {cleaned_count} 个文件")
    else:
        logger.info("   ✅ 没有需要清理的文件")

def main():
    """主函数"""
    import argparse
    
    parser=argparse.ArgumentParser(description="验证VeryFL - PoL路径配置")
    parser.add_argument('--show-structure', action='store_true', help="显示路径结构")
    parser.add_argument('--clean', action='store_true', help="清理空目录")
    
    args=parser.parse_args()
    
    if args.show_structure:
        show_path_structure()
        return
    
    if args.clean:
        clean_empty_directories()
        return
    
    # 默认执行验证
    success=verify_path_configuration()
    
    if success:
        logger.info("\n🎉 路径配置验证成功！")
        logger.info("💡 提示: 使用 --show-structure 查看路径结构")
        logger.info("💡 提示: 使用 --clean 清理空目录")
    else:
        logger.error("\n❌ 路径配置验证失败！")
        sys.exit(1)

if __name__== "__main__":
    main()
