"""
支持PoL的客户端类
集成学习证明生成和提交功能
"""

import logging
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from copy import deepcopy
from typing import Dict, Any, Optional, List

from client.clients import Client
from client.base.baseTrainer import BaseTrainer
from client.trainer.polTrainer import PoLTrainer, PoLFedProxTrainer
from pol.pol_verifier import PoLVerifier
from chainfl.pol_blockchain_client import pol_blockchain_client

logger=logging.getLogger(__name__)

class PoLClient(Client):
    """支持学习证明的客户端"""
    
    def __init__(self, 
                 client_id: str,
                 dataloader: DataLoader,
                 model: nn.<PERSON><PERSON><PERSON>,
                 trainer: BaseTrainer,
                 args: dict={},
                 test_dataloader: DataLoader=None,
                 watermarks: dict={},
                 pol_config: dict=None):
        """
        初始化PoL客户端
        
        Args:
            client_id: 客户端ID
            dataloader: 训练数据加载器
            model: 神经网络模型
            trainer: 训练器类
            args: 训练参数
            test_dataloader: 测试数据加载器
            watermarks: 水印参数
            pol_config: PoL配置参数
        """
        super().__init__(client_id, dataloader, model, trainer, args, test_dataloader, watermarks)
        
        # PoL配置
        self.pol_config=pol_config or {}
        self.enable_pol = self.pol_config.get('enable_pol', True)
        self.enable_blockchain=self.pol_config.get('enable_blockchain', True)

        # 存储PoL证明
        self.pol_proof=None

        # 初始化增强的PoL验证器
        if self.enable_pol:
            verification_budget_Q = self.pol_config.get('verification_budget_Q', 1)
            self.pol_verifier=PoLVerifier(
                verification_budget_Q = verification_budget_Q,
                enable_multi_metric=True  # 启用多距离度量验证
            )
        else:
            self.pol_verifier=None

        # 区块链相关
        self.client_address = self._generate_client_address()
        self.actual_blockchain_address=None  # 实际使用的区块链地址
        self.pol_submission_result = None
        
        logger.info(f"PoL客户端 {client_id} 初始化完成，PoL功能: {'启用' if self.enable_pol else '禁用'}")
    
    def train(self, epoch: int):
        """
        训练方法，生成PoL证明

        Args:
            epoch: 当前训练轮数
        """
        logger.info(f"🚀 客户端 {self.client_id} 开始训练，PoL功能: {'启用' if self.enable_pol else '禁用'}")

        if self.enable_pol:
            # 使用PoL训练器
            logger.info(f"🔧 客户端 {self.client_id} 使用PoL训练器")
            if isinstance(self.trainer, type):
                # 如果trainer是类，需要实例化为PoL训练器
                pol_trainer=self._create_pol_trainer()
                ret_list, self.pol_proof=pol_trainer.train(
                    self.args.get('num_steps', 1),
                    self.client_id
                )
                logger.info(f"✅ 客户端 {self.client_id} PoL训练完成，证明生成: {'成功' if self.pol_proof else '失败'}")
            else:
                # 如果trainer已经是实例，直接使用
                ret_list=self.trainer.train(self.args.get('num_steps', 1))
                self.pol_proof=None
                logger.warning(f"⚠️ 客户端 {self.client_id} 使用非PoL训练器实例")
        else:
            # 使用普通训练器
            logger.info(f"🔧 客户端 {self.client_id} 使用普通训练器")
            cal=self.trainer(self.model, self.dataloader, torch.nn.CrossEntropyLoss(), self.args)
            ret_list=cal.train(self.args.get('num_steps', 1))
            self.pol_proof=None
            logger.info(f"✅ 客户端 {self.client_id} 普通训练完成")

        self.show_train_result(epoch, ret_list)
        logger.info(f"📊 客户端 {self.client_id} 训练结果显示完成")

        # 记录PoL信息（移除同步区块链提交，改为异步批量提交）
        if self.pol_proof:
            logger.info(f"客户端 {self.client_id} 生成PoL证明，总步数: {self.pol_proof['total_steps']}")
            # 注意：区块链提交现在由task.py中的异步批量提交处理
            logger.debug(f"客户端 {self.client_id} PoL证明已准备好，等待批量提交到区块链")
    
    def _create_pol_trainer(self):
        """创建PoL训练器实例"""
        trainer_name=self.trainer.__name__ if hasattr(self.trainer, '__name__') else str(self.trainer)
        
        if 'fedprox' in trainer_name.lower():
            # 创建PoL FedProx训练器
            return PoLFedProxTrainer(
                self.model, 
                self.dataloader, 
                torch.nn.CrossEntropyLoss(), 
                self.args,
                self.watermarks,
                self.pol_config
            )
        else:
            # 创建普通PoL训练器
            return PoLTrainer(
                self.model, 
                self.dataloader, 
                torch.nn.CrossEntropyLoss(), 
                self.args,
                self.watermarks,
                self.pol_config
            )
    
    def get_model_update_with_proof(self) -> Dict[str, Any]:
        """
        获取模型更新和PoL证明
        
        Returns:
            包含模型状态和PoL证明的字典
        """
        update_data={
            'client_id': self.client_id,
            'state_dict': self.model.state_dict(),
            'pol_proof': self.pol_proof,
            'has_pol': self.pol_proof is not None
        }
        
        if self.pol_proof:
            # 添加PoL摘要信息
            update_data['pol_summary'] = {
                'total_steps': self.pol_proof['total_steps'],
                'checkpoint_count': len(self.pol_proof['checkpoints']),
                'proof_hash': self.pol_proof['proof_hash']
            }
        
        return update_data
    
    def verify_own_proof(self) -> bool:
        """
        验证自己的PoL证明
        
        Returns:
            验证结果
        """
        if not self.enable_pol:
            logger.info(f"客户端 {self.client_id} 未启用PoL，验证通过")
            return True  # PoL未启用时验证通过（向后兼容）

        if not self.pol_proof:
            logger.error(f"客户端 {self.client_id} 启用了PoL但缺少证明")
            return False  # 启用PoL但缺少证明时验证失败

        if not self.pol_verifier:
            logger.error(f"客户端 {self.client_id} 缺少PoL验证器")
            return False  # 缺少验证器时验证失败
        
        try:
            # 进行快速验证（不包括重现验证）
            is_valid=self.pol_verifier.quick_verify(self.pol_proof)
            
            if is_valid:
                logger.info(f"客户端 {self.client_id} PoL证明自验证通过")
            else:
                logger.warning(f"客户端 {self.client_id} PoL证明自验证失败")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"客户端 {self.client_id} PoL证明自验证过程中发生错误: {e}")
            return False
    
    def get_pol_summary(self) -> Dict[str, Any]:
        """
        获取PoL证明摘要
        
        Returns:
            PoL证明摘要信息
        """
        if not self.pol_proof:
            return {'has_pol': False}
        
        return {
            'has_pol': True,
            'client_id': self.pol_proof['client_id'],
            'total_steps': self.pol_proof['total_steps'],
            'save_freq': self.pol_proof['save_freq'],
            'checkpoint_count': len(self.pol_proof['checkpoints']),
            'batch_count': len(self.pol_proof['batch_indices']),
            'proof_hash': self.pol_proof['proof_hash']
        }
    
    def _generate_client_address(self) -> str:
        """生成客户端区块链地址"""
        import hashlib
        import time
        # 基于客户端ID和时间戳生成唯一地址，避免冲突
        unique_string=f"{self.client_id}_{int(time.time() * 1000000)}"
        address_hash=hashlib.sha256(unique_string.encode()).hexdigest()
        return f"0x{address_hash[: 40]}"

    def _submit_pol_to_blockchain(self, max_retries: int=3):
        """提交PoL证明到区块链（带重试机制）"""
        if not self.enable_blockchain:
            logger.info(f"客户端 {self.client_id} 区块链功能已禁用，跳过PoL提交")
            return

        import time
        import random

        # 添加随机延迟避免同时提交导致的nonce冲突
        delay=random.uniform(0.1, 1.0)  # 0.1 - 1.0秒随机延迟
        time.sleep(delay)

        for attempt in range(max_retries):
            try:
                # 确保pol_blockchain_client已初始化
                from chainfl.pol_blockchain_client import pol_blockchain_client

                # 获取实际使用的区块链地址（在提交前）
                client_account=pol_blockchain_client._get_client_account(self.client_address)
                self.actual_blockchain_address=client_account.address

                logger.info(f"客户端 {self.client_id} 尝试提交PoL证明 (第{attempt + 1}/{max_retries}次)")

                # 提交PoL证明哈希到区块链
                self.pol_submission_result=pol_blockchain_client.submit_pol_proof(
                    client_address = self.client_address,
                    pol_proof=self.pol_proof,
                    ipfs_hash=None
                )

                if self.pol_submission_result.success:
                    logger.info(f"🔗 客户端 {self.client_id} PoL证明已提交到区块链")
                    logger.info(f"   - 交易哈希: {self.pol_submission_result.tx_hash}")
                    logger.info(f"   - Gas消耗: {self.pol_submission_result.gas_used}")
                    logger.info(f"   - 证明步数: {self.pol_proof['total_steps']}")
                    logger.info(f"   - 区块链地址: {self.actual_blockchain_address}")
                    return  # 成功提交，退出重试循环
                else:
                    logger.warning(f"⚠️ 客户端 {self.client_id} PoL证明提交失败 (第{attempt + 1}次): {self.pol_submission_result.error_message}")
                    if attempt < max_retries - 1:
                        # 指数退避重试
                        retry_delay=(2 ** attempt) + random.uniform(0, 1)
                        logger.info(f"   - 将在 {retry_delay:.1f} 秒后重试...")
                        time.sleep(retry_delay)

            except Exception as e:
                error_msg=str(e)
                if "Connection refused" in error_msg or "HTTPConnectionPool" in error_msg:
                    logger.warning(f"客户端 {self.client_id} 区块链连接失败 (第{attempt + 1}次): 区块链节点未运行")
                    logger.warning("   - 提示: 请启动Ganache或其他区块链节点")
                else:
                    logger.error(f"客户端 {self.client_id} 提交PoL到区块链时发生错误 (第{attempt + 1}次): {e}")

                if attempt < max_retries - 1:
                    # 指数退避重试
                    retry_delay=(2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"   - 将在 {retry_delay:.1f} 秒后重试...")
                    time.sleep(retry_delay)

        # 所有重试都失败了
        logger.error(f"❌ 客户端 {self.client_id} PoL证明提交最终失败，已重试 {max_retries} 次")
        logger.error(f"   - 这可能导致该客户端的PoL记录数量不一致")
        logger.error(f"   - 建议检查网络连接和区块链节点状态")

    def get_blockchain_reputation(self) -> Dict[str, Any]:
        """获取区块链上的信誉信息"""
        if not self.enable_blockchain:
            return {'blockchain_disabled': True, 'reputation_score': 500}

        try:
            from chainfl.pol_blockchain_client import pol_blockchain_client

            # 使用实际的区块链地址查询
            query_address=self.actual_blockchain_address if self.actual_blockchain_address else self.client_address
            if not self.actual_blockchain_address:
                # 如果没有实际地址，获取对应的区块链账户地址
                client_account = pol_blockchain_client._get_client_account(self.client_address)
                query_address=client_account.address

            reputation = pol_blockchain_client.get_client_reputation(query_address)
            return {
                'total_submissions': reputation.total_submissions,
                'valid_submissions': reputation.valid_submissions,
                'reputation_score': reputation.reputation_score,
                'is_blacklisted': reputation.is_blacklisted,
                'success_rate': reputation.valid_submissions / max(1, reputation.total_submissions)
            }
        except Exception as e:
            logger.error(f"获取区块链信誉信息失败: {e}")
            return {'error': str(e)}

    def get_blockchain_pol_records(self) -> List[Dict[str, Any]]:
        """获取区块链上的PoL记录"""
        if not self.enable_blockchain:
            return []

        try:
            from chainfl.pol_blockchain_client import pol_blockchain_client

            # 使用实际的区块链地址查询
            query_address=self.actual_blockchain_address if self.actual_blockchain_address else self.client_address
            if not self.actual_blockchain_address:
                # 如果没有实际地址，获取对应的区块链账户地址
                client_account = pol_blockchain_client._get_client_account(self.client_address)
                query_address=client_account.address

            return pol_blockchain_client.get_pol_records(query_address)
        except Exception as e:
            logger.error(f"获取区块链PoL记录失败: {e}")
            return []

    def reset_pol_state(self):
        """重置PoL状态"""
        self.pol_proof=None
        self.pol_submission_result = None
        logger.info(f"客户端 {self.client_id} PoL状态已重置")


class PoLSignClient(PoLClient):
    """支持PoL和签名的客户端"""
    
    def train(self, epoch: int, watermarks: dict=None):
        """
        训练方法，支持水印和PoL
        
        Args:
            epoch: 当前训练轮数
            watermarks: 水印参数
        """
        if watermarks:
            self.watermarks=watermarks
        
        # 调用父类的训练方法
        super().train(epoch)
        
        # 额外的签名相关处理可以在这里添加
        logger.info(f"PoL签名客户端 {self.client_id} 完成训练，轮数: {epoch}")
    
    def get_model_update_with_proof_and_signature(self) -> Dict[str, Any]:
        """
        获取包含模型更新、PoL证明和签名的数据
        
        Returns:
            完整的客户端更新数据
        """
        update_data=self.get_model_update_with_proof()
        
        # 添加签名相关信息
        update_data['watermarks'] = self.watermarks
        update_data['has_signature'] = bool(self.watermarks)
        
        return update_data
