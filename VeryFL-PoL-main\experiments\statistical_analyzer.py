#!/usr/bin/env python3
"""
统计分析模块
提供显著性检验、置信区间计算等统计分析功能
"""

import numpy as np
import pandas as pd
import scipy.stats as stats
from typing import Dict, List, Any, Tuple, Optional
import logging
from dataclasses import dataclass

logger=logging.getLogger(__name__)

@dataclass
class StatisticalResult:
    """统计分析结果"""
    test_name: str
    statistic: float
    p_value: float
    is_significant: bool
    confidence_interval: Optional[Tuple[float, float]] = None
    effect_size: Optional[float] = None
    interpretation: str=""

class StatisticalAnalyzer:
    """统计分析器"""
    
    def __init__(self, alpha: float=0.05):
        """
        初始化统计分析器
        
        Args:
            alpha: 显著性水平，默认0.05
        """
        self.alpha=alpha
        self.logger = logging.getLogger(__name__)
    
    def t_test_independent(self, group1: List[float], group2: List[float], 
                          group1_name: str="Group1", group2_name: str="Group2") -> StatisticalResult:
        """
        独立样本t检验
        
        Args:
            group1: 第一组数据
            group2: 第二组数据
            group1_name: 第一组名称
            group2_name: 第二组名称
            
        Returns:
            统计分析结果
        """
        try:
            # 进行独立样本t检验
            statistic, p_value=stats.ttest_ind(group1, group2)
            is_significant=p_value < self.alpha
            
            # 计算置信区间
            mean1, mean2=np.mean(group1), np.mean(group2)
            diff=mean1 - mean2
            se_diff = np.sqrt(np.var(group1, ddof=1)/len(group1) + np.var(group2, ddof=1)/len(group2))
            df=len(group1) + len(group2) - 2
            t_critical=stats.t.ppf(1 - self.alpha/2, df)
            ci_lower=diff - t_critical * se_diff
            ci_upper = diff + t_critical * se_diff
            
            # 计算Cohen's d (效应量)
            pooled_std=np.sqrt(((len(group1)-1)*np.var(group1, ddof=1) + 
                                 (len(group2)-1)*np.var(group2, ddof=1)) / df)
            cohens_d=diff / pooled_std if pooled_std > 0 else 0
            
            # 解释结果
            if is_significant:
                direction = "显著高于" if diff > 0 else "显著低于"
                interpretation = f"{group1_name} {direction} {group2_name} (p={p_value:.4f}, Cohen's d={cohens_d:.3f})"
            else:
                interpretation=f"{group1_name} 与 {group2_name} 无显著差异 (p={p_value:.4f})"
            
            return StatisticalResult(
                test_name="独立样本t检验",
                statistic=statistic,
                p_value=p_value,
                is_significant=is_significant,
                confidence_interval=(ci_lower, ci_upper),
                effect_size=cohens_d,
                interpretation=interpretation
            )
            
        except Exception as e:
            self.logger.error(f"t检验失败: {e}")
            return StatisticalResult(
                test_name="独立样本t检验",
                statistic=0.0,
                p_value=1.0,
                is_significant=False,
                interpretation=f"检验失败: {e}"
            )
    
    def paired_t_test(self, before: List[float], after: List[float]) -> StatisticalResult:
        """
        配对样本t检验
        
        Args:
            before: 处理前数据
            after: 处理后数据
            
        Returns:
            统计分析结果
        """
        try:
            statistic, p_value=stats.ttest_rel(before, after)
            is_significant=p_value < self.alpha
            
            # 计算差值的置信区间
            diff = np.array(after) - np.array(before)
            mean_diff=np.mean(diff)
            se_diff=stats.sem(diff)
            df=len(diff) - 1
            t_critical=stats.t.ppf(1 - self.alpha/2, df)
            ci_lower=mean_diff - t_critical * se_diff
            ci_upper = mean_diff + t_critical * se_diff
            
            # 计算效应量
            cohens_d = mean_diff / np.std(diff, ddof=1) if np.std(diff, ddof=1) > 0 else 0
            
            # 解释结果
            if is_significant:
                direction="显著提升" if mean_diff > 0 else "显著下降"
                interpretation = f"处理后 {direction} (p={p_value:.4f}, Cohen's d={cohens_d:.3f})"
            else:
                interpretation=f"处理前后无显著差异 (p={p_value:.4f})"
            
            return StatisticalResult(
                test_name="配对样本t检验",
                statistic=statistic,
                p_value=p_value,
                is_significant=is_significant,
                confidence_interval=(ci_lower, ci_upper),
                effect_size=cohens_d,
                interpretation=interpretation
            )
            
        except Exception as e:
            self.logger.error(f"配对t检验失败: {e}")
            return StatisticalResult(
                test_name="配对样本t检验",
                statistic=0.0,
                p_value=1.0,
                is_significant=False,
                interpretation=f"检验失败: {e}"
            )
    
    def anova_one_way(self, groups: Dict[str, List[float]]) -> StatisticalResult:
        """
        单因素方差分析
        
        Args:
            groups: 各组数据，格式为 {组名: [数据]}
            
        Returns:
            统计分析结果
        """
        try:
            group_names=list(groups.keys())
            group_data=list(groups.values())
            
            # 进行单因素ANOVA
            statistic, p_value=stats.f_oneway(*group_data)
            is_significant=p_value < self.alpha
            
            # 计算效应量 (eta squared)
            group_means=[np.mean(group) for group in group_data]
            overall_mean=np.mean([val for group in group_data for val in group])
            
            ss_between=sum(len(group) * (mean - overall_mean)**2 
                           for group, mean in zip(group_data, group_means))
            ss_total=sum((val - overall_mean)**2 
                          for group in group_data for val in group)
            
            eta_squared=ss_between / ss_total if ss_total > 0 else 0
            
            # 解释结果
            if is_significant:
                interpretation = f"各组间存在显著差异 (F={statistic:.3f}, p={p_value:.4f}, η²={eta_squared:.3f})"
            else:
                interpretation=f"各组间无显著差异 (F={statistic:.3f}, p={p_value:.4f})"
            
            return StatisticalResult(
                test_name="单因素方差分析",
                statistic=statistic,
                p_value=p_value,
                is_significant=is_significant,
                effect_size=eta_squared,
                interpretation=interpretation
            )
            
        except Exception as e:
            self.logger.error(f"ANOVA分析失败: {e}")
            return StatisticalResult(
                test_name="单因素方差分析",
                statistic=0.0,
                p_value=1.0,
                is_significant=False,
                interpretation=f"分析失败: {e}"
            )
    
    def calculate_confidence_interval(self, data: List[float], confidence: float=0.95) -> Tuple[float, float]:
        """
        计算置信区间
        
        Args:
            data: 数据列表
            confidence: 置信水平，默认0.95
            
        Returns:
            置信区间 (下界, 上界)
        """
        try:
            mean=np.mean(data)
            se=stats.sem(data)
            df=len(data) - 1
            t_critical=stats.t.ppf((1 + confidence) / 2, df)
            margin_error=t_critical * se
            
            return (mean - margin_error, mean + margin_error)
            
        except Exception as e:
            self.logger.error(f"置信区间计算失败: {e}")
            return (0.0, 0.0)
    
    def descriptive_statistics(self, data: List[float], name: str="Data") -> Dict[str, Any]:
        """
        描述性统计
        
        Args:
            data: 数据列表
            name: 数据名称
            
        Returns:
            描述性统计结果
        """
        try:
            return {
                'name': name,
                'count': len(data),
                'mean': np.mean(data),
                'std': np.std(data, ddof=1),
                'min': np.min(data),
                'max': np.max(data),
                'median': np.median(data),
                'q25': np.percentile(data, 25),
                'q75': np.percentile(data, 75),
                'ci_95': self.calculate_confidence_interval(data, 0.95)
            }
        except Exception as e:
            self.logger.error(f"描述性统计失败: {e}")
            return {'name': name, 'error': str(e)}
    
    def compare_multiple_groups(self, groups: Dict[str, List[float]]) -> Dict[str, Any]:
        """
        多组比较分析
        
        Args:
            groups: 各组数据
            
        Returns:
            完整的比较分析结果
        """
        results={
            'descriptive_stats': {},
            'anova_result': None,
            'pairwise_comparisons': []
        }
        
        # 描述性统计
        for name, data in groups.items():
            results['descriptive_stats'][name] = self.descriptive_statistics(data, name)
        
        # 方差分析
        if len(groups) > 2:
            results['anova_result'] = self.anova_one_way(groups)
        
        # 两两比较
        group_names=list(groups.keys())
        for i in range(len(group_names)):
            for j in range(i + 1, len(group_names)):
                name1, name2=group_names[i], group_names[j]
                comparison=self.t_test_independent(
                    groups[name1], groups[name2], name1, name2
                )
                results['pairwise_comparisons'].append(comparison)
        
        return results
