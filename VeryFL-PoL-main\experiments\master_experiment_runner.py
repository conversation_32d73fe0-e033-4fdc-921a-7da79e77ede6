#!/usr/bin/env python3
"""
主实验控制器
整合所有5个实验，生成完整的实验报告
"""

import os
import sys
import json
import time
import logging
import argparse
import pandas as pd
from typing import Dict, List, Any
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from experiments.parallel_executor import ParallelExecutor, EXPERIMENT_SUITES
from experiments.statistical_analyzer import StatisticalAnalyzer

# 尝试导入可选的实验模块
try:
    from experiments.sota_comparison import SOTAComparison
    HAS_SOTA=True
except ImportError:
    HAS_SOTA = False

try:
    from experiments.compression_comparison import FederatedCompressionTester
    HAS_COMPRESSION = True
except ImportError:
    HAS_COMPRESSION = False

try:
    from experiments.network_simulator import NetworkSimulator
    HAS_NETWORK = True
except ImportError:
    HAS_NETWORK = False

try:
    from experiments.ablation_study import AblationStudy
    HAS_ABLATION = True
except ImportError:
    HAS_ABLATION = False

class MasterExperimentRunner:
    """主实验控制器"""
    
    def __init__(self, output_dir: str=None):
        self.output_dir=output_dir or f"experiments/master_experiment_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{self.output_dir}/master_experiment.log'),
                logging.StreamHandler()
            ]
        )
        self.logger=logging.getLogger(__name__)
        
        # 初始化各个实验模块
        self.parallel_executor=ParallelExecutor(max_parallel = 2,
                                                output_dir=f"{self.output_dir}/parallel_results")
        self.statistical_analyzer=StatisticalAnalyzer()

        # 初始化可选模块
        self.sota_comparison=SOTAComparison(f"{self.output_dir}/sota_results") if HAS_SOTA else None
        self.compression_tester=FederatedCompressionTester(f"{self.output_dir}/compression_results") if HAS_COMPRESSION else None
        self.network_simulator=NetworkSimulator(f"{self.output_dir}/network_results") if HAS_NETWORK else None
        self.ablation_study=AblationStudy(f"{self.output_dir}/ablation_results") if HAS_ABLATION else None
        
        self.experiment_results={}
    
    def run_experiment_1_performance(self) -> Dict[str, Any]:
        """实验1：基础性能对比"""
        self.logger.info("🚀 开始实验1：基础性能对比")
        
        # 使用并行执行器运行性能对比实验
        configs=EXPERIMENT_SUITES["performance"]
        results = self.parallel_executor.run_experiments(configs)
        
        # 使用SOTA对比模块进行详细分析（如果可用）
        sota_results=None
        if self.sota_comparison:
            try:
                sota_results = self.sota_comparison.run_comparison()
            except Exception as e:
                self.logger.warning(f"SOTA对比分析失败: {e}")
                sota_results=None
        
        return {
            "parallel_results": results,
            "sota_analysis": sota_results,
            "summary": self._summarize_performance_results(results)
        }
    
    def run_experiment_2_attack_defense(self) -> Dict[str, Any]:
        """实验2：攻击防御效果"""
        self.logger.info("🚀 开始实验2：攻击防御效果")
        
        # 使用并行执行器运行攻击防御实验
        configs=EXPERIMENT_SUITES["attack_defense"]
        results = self.parallel_executor.run_experiments(configs)
        
        return {
            "attack_results": results,
            "summary": self._summarize_attack_results(results)
        }
    
    def run_experiment_3_compression(self) -> Dict[str, Any]:
        """实验3：压缩效果分析"""
        self.logger.info("🚀 开始实验3：压缩效果分析")
        
        # 使用压缩对比模块（如果可用）
        compression_results=None
        if self.compression_tester:
            try:
                compression_results = self.compression_tester.run_comprehensive_comparison()
            except Exception as e:
                self.logger.warning(f"压缩效果分析失败: {e}")
                compression_results=None
        
        # 也运行并行执行器的压缩实验
        configs = EXPERIMENT_SUITES["compression"]
        parallel_results = self.parallel_executor.run_experiments(configs)
        
        return {
            "compression_analysis": compression_results,
            "parallel_results": parallel_results,
            "summary": self._summarize_compression_results(compression_results)
        }
    
    def run_experiment_4_scalability(self) -> Dict[str, Any]:
        """实验4：可扩展性测试"""
        self.logger.info("🚀 开始实验4：可扩展性测试")
        
        # 使用网络模拟器（如果可用）
        scalability_results=None
        if self.network_simulator:
            try:
                scalability_results = self.network_simulator.run_scalability_test()
            except Exception as e:
                self.logger.warning(f"可扩展性测试失败: {e}")
                scalability_results=None
        
        return {
            "scalability_results": scalability_results,
            "summary": self._summarize_scalability_results(scalability_results)
        }
    
    def run_experiment_5_ablation(self) -> Dict[str, Any]:
        """实验5：消融实验"""
        self.logger.info("🚀 开始实验5：消融实验")
        
        # 使用消融研究模块（如果可用）
        ablation_results=None
        if self.ablation_study:
            try:
                ablation_results = self.ablation_study.run_ablation_study()
            except Exception as e:
                self.logger.warning(f"消融实验失败: {e}")
                ablation_results=None
        
        return {
            "ablation_results": ablation_results,
            "summary": self._summarize_ablation_results(ablation_results)
        }
    
    def run_all_experiments(self) -> Dict[str, Any]:
        """运行所有5个实验"""
        self.logger.info("🎯 开始运行完整的5个实验套件")
        start_time=time.time()
        
        # 运行各个实验
        self.experiment_results["experiment_1_performance"] = self.run_experiment_1_performance()
        self.experiment_results["experiment_2_attack_defense"] = self.run_experiment_2_attack_defense()
        self.experiment_results["experiment_3_compression"] = self.run_experiment_3_compression()
        self.experiment_results["experiment_4_scalability"] = self.run_experiment_4_scalability()
        self.experiment_results["experiment_5_ablation"] = self.run_experiment_5_ablation()
        
        total_time=time.time() - start_time
        
        # 生成综合报告
        comprehensive_report=self.generate_comprehensive_report()
        
        self.logger.info(f"✅ 所有实验完成，总耗时: {total_time:.1f}秒")
        return {
            "experiment_results": self.experiment_results,
            "comprehensive_report": comprehensive_report,
            "total_time": total_time
        }
    
    def _summarize_performance_results(self, results) -> Dict[str, Any]:
        """总结性能对比结果"""
        # 简化的总结逻辑
        successful=[r for r in results if r.success]
        return {
            "total_experiments": len(results),
            "successful_experiments": len(successful),
            "success_rate": len(successful) / len(results) if results else 0
        }
    
    def _summarize_attack_results(self, results) -> Dict[str, Any]:
        """总结攻击防御结果"""
        successful=[r for r in results if r.success]
        return {
            "total_experiments": len(results),
            "successful_experiments": len(successful),
            "success_rate": len(successful) / len(results) if results else 0
        }
    
    def _summarize_compression_results(self, results) -> Dict[str, Any]:
        """总结压缩效果结果"""
        return {
            "compression_methods_tested": len(results) if results else 0,
            "analysis_completed": results is not None
        }
    
    def _summarize_scalability_results(self, results) -> Dict[str, Any]:
        """总结可扩展性结果"""
        return {
            "scalability_test_completed": results is not None,
            "analysis_available": results is not None
        }
    
    def _summarize_ablation_results(self, results) -> Dict[str, Any]:
        """总结消融实验结果"""
        return {
            "ablation_study_completed": results is not None,
            "component_analysis_available": results is not None
        }
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成综合实验报告"""
        self.logger.info("📊 生成综合实验报告")
        
        report={
            "experiment_overview": {
                "total_experiments": 5,
                "completed_experiments": len(self.experiment_results),
                "timestamp": datetime.now().isoformat(),
                "output_directory": self.output_dir
            },
            "experiment_summaries": {},
            "statistical_analysis": {},
            "conclusions": []
        }
        
        # 添加各实验的总结
        for exp_name, exp_data in self.experiment_results.items():
            if "summary" in exp_data:
                report["experiment_summaries"][exp_name] = exp_data["summary"]
        
        # 保存报告
        report_file=f"{self.output_dir}/comprehensive_report.json"
        with open(report_file, 'w', encoding='utf - 8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 生成Markdown报告
        self.generate_markdown_report(report)
        
        self.logger.info(f"📄 综合报告已保存: {report_file}")
        return report
    
    def generate_markdown_report(self, report: Dict[str, Any]):
        """生成Markdown格式的报告"""
        markdown_file=f"{self.output_dir}/experiment_report.md"
        
        with open(markdown_file, 'w', encoding='utf - 8') as f:
            f.write("# VeryFL - PoL 完整实验报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 实验概览\n\n")
            overview=report["experiment_overview"]
            f.write(f"- 总实验数: {overview['total_experiments']}\n")
            f.write(f"- 完成实验数: {overview['completed_experiments']}\n")
            f.write(f"- 输出目录: {overview['output_directory']}\n\n")
            
            f.write("## 实验结果总结\n\n")
            for exp_name, summary in report["experiment_summaries"].items():
                f.write(f"### {exp_name}\n\n")
                for key, value in summary.items():
                    f.write(f"- {key}: {value}\n")
                f.write("\n")
            
            f.write("## 详细结果\n\n")
            f.write("详细的实验数据和分析结果请查看各子目录中的具体文件。\n\n")
            
            f.write("## 文件结构\n\n")
            f.write("```\n")
            f.write(f"{self.output_dir}/\n")
            f.write("├── parallel_results/     # 并行实验结果\n")
            f.write("├── sota_results/         # SOTA对比结果\n")
            f.write("├── compression_results/  # 压缩效果分析\n")
            f.write("├── network_results/      # 网络可扩展性测试\n")
            f.write("├── ablation_results/     # 消融实验结果\n")
            f.write("├── comprehensive_report.json  # JSON格式综合报告\n")
            f.write("└── experiment_report.md       # 本Markdown报告\n")
            f.write("```\n")
        
        self.logger.info(f"📄 Markdown报告已保存: {markdown_file}")

def main():
    parser=argparse.ArgumentParser(description="VeryFL - PoL 主实验控制器")
    parser.add_argument("--experiment", type=str, choices=["1", "2", "3", "4", "5", "all"], 
                       default="all", help="运行指定实验 (1 - 5) 或全部实验")
    parser.add_argument("--output-dir", type=str, help="输出目录")
    
    args=parser.parse_args()
    
    # 创建主实验控制器
    runner=MasterExperimentRunner(args.output_dir)
    
    # 根据参数运行相应实验
    if args.experiment== "1":
        runner.run_experiment_1_performance()
    elif args.experiment== "2":
        runner.run_experiment_2_attack_defense()
    elif args.experiment== "3":
        runner.run_experiment_3_compression()
    elif args.experiment== "4":
        runner.run_experiment_4_scalability()
    elif args.experiment== "5":
        runner.run_experiment_5_ablation()
    else:  # "all"
        runner.run_all_experiments()
    
    return 0

if __name__== "__main__":
    exit(main())
