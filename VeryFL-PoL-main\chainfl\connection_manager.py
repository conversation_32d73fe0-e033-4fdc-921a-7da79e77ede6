"""
区块链连接管理器
解决EventEmitter监听器数量超限问题
"""

import logging
import threading
from typing import Optional, Dict, Any
import time

logger=logging.getLogger(__name__)

class BlockchainConnectionManager:
    """区块链连接管理器 - 单例模式"""
    
    _instance=None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance=super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self._initialized=True
        self.connection = None
        self.accounts = None
        self.contracts = {}
        self.connection_count = 0
        self._connection_lock = threading.Lock()
        
        logger.info("🔗 初始化区块链连接管理器")
    
    def get_connection(self, client_id: str=None):
        """获取区块链连接（复用现有连接）"""
        with self._connection_lock:
            if self.connection is None:
                self._establish_connection()
            
            self.connection_count += 1
            if client_id:
                logger.debug(f"客户端 {client_id} 获取区块链连接 (总连接数: {self.connection_count})")
            
            return self.connection
    
    def release_connection(self, client_id: str=None):
        """释放区块链连接"""
        with self._connection_lock:
            if self.connection_count > 0:
                self.connection_count -= 1
                
            if client_id:
                logger.debug(f"客户端 {client_id} 释放区块链连接 (剩余连接数: {self.connection_count})")
    
    def _establish_connection(self):
        """建立区块链连接 - 彻底重写版本"""
        try:
            import os
            import time
            import random

            # 确保在正确的项目上下文中
            current_dir=os.path.dirname(os.path.abspath(__file__))
            chainenv_path=os.path.join(current_dir, "..", "chainEnv")
            chainenv_path=os.path.abspath(chainenv_path)

            # 保存当前工作目录
            original_cwd=os.getcwd()

            try:
                # 切换到chainEnv目录，确保Brownie使用正确的配置
                os.chdir(chainenv_path)
                logger.info(f"🔄 切换到chainEnv目录: {chainenv_path}")

                # 重新导入brownie以确保使用正确的配置
                import importlib
                import sys

                # 清理brownie模块缓存
                modules_to_reload=[name for name in sys.modules.keys() if name.startswith('brownie')]
                for module_name in modules_to_reload:
                    if module_name in sys.modules:
                        del sys.modules[module_name]

                # 重新导入brownie
                from brownie import network, accounts, project

                # 处理并发项目加载 - 先加载项目再检查网络
                max_retries=3
                project_loaded = False
                for attempt in range(max_retries):
                    try:
                        if attempt > 0:
                            delay=random.uniform(0.1, 0.5)
                            time.sleep(delay)
                            logger.info(f"🔄 重试加载项目 (尝试 {attempt + 1}/{max_retries})")

                        # 尝试加载项目
                        if not hasattr(self, '_brownie_project') or not self._brownie_project:
                            self._brownie_project=project.load()  # 在chainEnv目录下直接加载
                            logger.info(f"✅ 成功加载Brownie项目")
                        project_loaded=True
                        break

                    except Exception as e:
                        if "already a project loaded with this name" in str(e):
                            logger.info("📋 项目已被其他进程加载，继续使用现有项目")
                            project_loaded=True
                            break
                        elif attempt == max_retries - 1:
                            logger.warning(f"⚠️ 项目加载失败: {e}")
                        else:
                            logger.info(f"⏳ 项目加载重试中... ({e})")

                # 重新检查网络配置（项目加载后可能会更新）
                available_networks=list(network.main.CONFIG.networks.keys())
                logger.info(f"📋 项目加载后可用网络: {available_networks}")

                # 智能网络选择 - 按优先级尝试连接
                network_candidates=['development', 'geth - dev', 'hardhat', 'anvil']
                selected_network=None

                for candidate in network_candidates:
                    if candidate in available_networks:
                        selected_network = candidate
                        logger.info(f"🎯 选择网络: {selected_network}")
                        break

                if not selected_network:
                    raise RuntimeError(f"没有找到可用的本地开发网络。可用网络: {available_networks}")

                # 连接到选定的网络
                if not network.is_connected():
                    logger.info(f"🔗 尝试连接到{selected_network}网络...")
                    network.connect(selected_network)
                    logger.info(f"✅ 成功连接到{selected_network}网络")

                    # 记录网络信息到日志
                    logger.info(f"🌐 区块链网络信息:")
                    logger.info(f"   📡 网络名称: {selected_network}")
                    logger.info(f"   🔗 RPC地址: {network.web3.provider.endpoint_uri}")
                    logger.info(f"   ⛓️  Chain ID: {network.chain.id}")
                    logger.info(f"   💰 可用账户: {len(network.accounts)}")
                    logger.info(f"   🏦 默认账户: {network.accounts[0]}")
                else:
                    current_network=network.show_active()
                    logger.info(f"📋 网络已连接: {current_network}")
                    logger.info(f"🌐 当前区块链网络: {current_network}")
                    logger.info(f"   🔗 RPC地址: {network.web3.provider.endpoint_uri}")

                self.connection=network
                self.accounts = accounts

                # 部署合约（如果需要）
                self._deploy_contracts()

            finally:
                # 恢复原始工作目录
                os.chdir(original_cwd)
                logger.info(f"🔄 恢复工作目录: {original_cwd}")

        except Exception as e:
            logger.error(f"❌ 区块链连接失败: {e}")
            logger.error(f"❌ 错误详情: {type(e).__name__}: {str(e)}")
            self.connection=None
            raise
    
    def _deploy_contracts(self):
        """部署智能合约"""
        try:
            # 使用已加载的项目或者从全局获取
            if hasattr(self, '_brownie_project') and self._brownie_project:
                p=self._brownie_project
            else:
                # 备用方案：尝试从brownie获取已加载的项目
                from brownie import project
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_path=os.path.join(current_dir, "..", "chainEnv")

                try:
                    p=project.load(project_path = project_path)
                except Exception as e:
                    if "already a project loaded with this name" in str(e):
                        # 项目已经加载，跳过合约部署
                        logger.info("📋 项目已加载，跳过合约部署")
                        return  # 直接返回，不进行合约部署
                    else:
                        raise

            # 获取合约引用
            if p:
                watermarkNegotiation=p.watermarkNegotiation
                clientManager = p.clientManager
            # 如果p为None，说明合约已经在上面的except块中导入了

            server_account = self.accounts[0]
            
            # 设置统一的Gas配置 - 修复Gas价格问题
            deploy_config = {
                'from': server_account,
                'gas_limit': 8000000,  # 增加Gas限制确保部署成功
                'gas_price': ***********,  # 25 Gwei，略高于网络基础价格确保交易被接受
                'allow_revert': True,  # 允许回滚以便调试
            }

            # 部署水印协商合约 - 改进错误处理
            if len(watermarkNegotiation) == 0:
                logger.info("📝 部署水印协商合约...")
                try:
                    contract=watermarkNegotiation.deploy(deploy_config)
                    self.contracts['watermarkNegotiation'] = contract
                    logger.info(f"✅ 水印协商合约部署成功，Gas消耗: {contract.tx.gas_used}")
                except Exception as deploy_error:
                    logger.warning(f"⚠️ 水印协商合约部署失败: {deploy_error}")
                    logger.info("📝 跳过水印协商合约部署，继续其他合约...")
            else:
                self.contracts['watermarkNegotiation'] = watermarkNegotiation[0]
                logger.info("📝 使用现有水印协商合约")

            # 部署客户端管理合约 - 改进错误处理
            if len(clientManager) == 0:
                logger.info("👥 部署客户端管理合约...")
                try:
                    contract=clientManager.deploy(deploy_config)
                    self.contracts['clientManager'] = contract
                    logger.info(f"✅ 客户端管理合约部署成功，Gas消耗: {contract.tx.gas_used}")
                except Exception as deploy_error:
                    logger.warning(f"⚠️ 客户端管理合约部署失败: {deploy_error}")
                    logger.info("👥 跳过客户端管理合约部署，继续其他合约...")
            else:
                self.contracts['clientManager'] = clientManager[0]
                logger.info("👥 使用现有客户端管理合约")
                
        except Exception as e:
            logger.warning(f"⚠️ 合约部署失败: {e}")
    
    def get_contract(self, contract_name: str):
        """获取智能合约实例"""
        return self.contracts.get(contract_name)
    
    def get_account(self, index: int):
        """获取账户"""
        if self.accounts and index < len(self.accounts):
            return self.accounts[index]
        return None
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connection is not None
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            'connected': self.is_connected(),
            'connection_count': self.connection_count,
            'contracts_deployed': len(self.contracts),
            'available_accounts': len(self.accounts) if self.accounts else 0
        }

# 全局连接管理器实例
connection_manager=BlockchainConnectionManager()
