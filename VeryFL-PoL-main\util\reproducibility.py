"""
实验可重现性管理模块
确保所有实验结果的可重现性和一致性
"""

import os
import random
import platform
import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime

logger=logging.getLogger(__name__)

# 默认随机种子
DEFAULT_SEED=42

def set_global_random_seed(seed: int = DEFAULT_SEED):
    """
    设置全局随机种子确保实验可重现
    
    Args:
        seed: 随机种子值，默认为42
    """
    try:
        # Python内置random模块
        random.seed(seed)
        
        # NumPy随机种子
        import numpy as np
        np.random.seed(seed)
        
        # PyTorch随机种子
        import torch
        torch.manual_seed(seed)
        
        # CUDA随机种子（如果可用）
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)  # 多GPU情况
            
            # 确保CUDA操作的确定性
            torch.backends.cudnn.deterministic=True
            torch.backends.cudnn.benchmark = False
            
            # 设置CUDA环境变量（更严格的确定性）
            os.environ['CUBLAS_WORKSPACE_CONFIG'] = ': 4096: 8'
            
            logger.info(f"✅ 已设置CUDA确定性模式，种子: {seed}")
        
        logger.info(f"✅ 全局随机种子已设置: {seed}")
        
    except ImportError as e:
        logger.warning(f"⚠️ 部分随机种子设置失败: {e}")
    except Exception as e:
        logger.error(f"❌ 随机种子设置失败: {e}")

def get_reproducibility_info() -> Dict[str, Any]:
    """
    获取当前环境的可重现性信息
    
    Returns:
        包含环境信息的字典
    """
    info={
        'timestamp': datetime.now().isoformat(),
        'random_seed': DEFAULT_SEED,
        'python_version': platform.python_version(),
        'platform': platform.platform(),
        'deterministic_mode': True
    }
    
    try:
        import torch
        info.update({
            'pytorch_version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'cuda_version': torch.version.cuda if torch.cuda.is_available() else None,
            'cudnn_deterministic': torch.backends.cudnn.deterministic,
            'cudnn_benchmark': torch.backends.cudnn.benchmark
        })
        
        if torch.cuda.is_available():
            info.update({
                'cuda_device_count': torch.cuda.device_count(),
                'cuda_device_name': torch.cuda.get_device_name(0)
            })
    except ImportError:
        logger.warning("PyTorch未安装，跳过相关信息收集")
    
    try:
        import numpy as np
        info['numpy_version'] = np.__version__
    except ImportError:
        logger.warning("NumPy未安装，跳过版本信息")
    
    return info

def save_reproducibility_info(save_path: str, additional_info: Optional[Dict] = None):
    """
    保存可重现性信息到文件
    
    Args:
        save_path: 保存路径
        additional_info: 额外的信息字典
    """
    info=get_reproducibility_info()
    
    if additional_info:
        info.update(additional_info)
    
    try:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'w', encoding='utf - 8') as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
        logger.info(f"✅ 可重现性信息已保存: {save_path}")
    except Exception as e:
        logger.error(f"❌ 保存可重现性信息失败: {e}")

def verify_reproducibility(reference_file: str) -> bool:
    """
    验证当前环境与参考环境的一致性
    
    Args:
        reference_file: 参考环境信息文件路径
        
    Returns:
        是否一致
    """
    try:
        with open(reference_file, 'r', encoding='utf - 8') as f:
            reference_info=json.load(f)
        
        current_info=get_reproducibility_info()
        
        # 检查关键字段
        critical_fields=[
            'random_seed', 'pytorch_version', 'cuda_available',
            'deterministic_mode'
        ]
        
        inconsistencies=[]
        for field in critical_fields:
            if field in reference_info and field in current_info:
                if reference_info[field] != current_info[field]:
                    inconsistencies.append(f"{field}: {reference_info[field]} -> {current_info[field]}")
        
        if inconsistencies:
            logger.warning(f"⚠️ 环境不一致: {', '.join(inconsistencies)}")
            return False
        else:
            logger.info("✅ 环境一致性验证通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 环境一致性验证失败: {e}")
        return False

def ensure_reproducible_environment(seed: int=DEFAULT_SEED, 
                                   save_info: bool=True,
                                   save_path: Optional[str] = None):
    """
    确保实验环境的可重现性
    
    Args:
        seed: 随机种子
        save_info: 是否保存环境信息
        save_path: 环境信息保存路径
    """
    # 设置随机种子
    set_global_random_seed(seed)
    
    # 保存环境信息
    if save_info:
        if save_path is None:
            save_path=f"experiments/reproducibility_info_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        save_reproducibility_info(save_path)
    
    logger.info("🔒 可重现性环境已配置完成")

# 自动初始化（模块导入时执行）
def _auto_initialize():
    """模块导入时自动初始化"""
    try:
        set_global_random_seed(DEFAULT_SEED)
    except Exception as e:
        logger.warning(f"自动初始化失败: {e}")

# 执行自动初始化
_auto_initialize()
