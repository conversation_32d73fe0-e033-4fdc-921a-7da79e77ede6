#!/usr/bin/env python3
"""
修复参数错误的脚本
修复argparse参数名中的空格和其他语法错误
"""

import os
import re
from pathlib import Path

def fix_argument_errors():
    """修复参数错误"""
    print("🔧 修复参数错误...")
    
    python_files=list(Path('.').rglob('*.py'))
    fixed_files=[]
    
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content=f.read()
            
            original_content=content
            
            # 修复参数名中的空格
            # --client-num -> --client-num
            content = re.sub(r'--([a-zA-Z]+)\s*-\s*([a-zA-Z]+)', r'--\1-\2', content)
            
            # 修复参数赋值中的空格
            # type=int -> type=int
            content = re.sub(r'type\s*=\s*', 'type=', content)
            content=re.sub(r'default\s*=\s*', 'default=', content)
            content=re.sub(r'help\s*=\s*', 'help=', content)
            
            # 修复函数调用中的参数空格
            # func(param=value) -> func(param=value)
            content=re.sub(r'(\w+)\s*=\s*([^,\)]+)', r'\1=\2', content)
            
            if content != original_content:
                with open(py_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(str(py_file))
                
        except Exception as e:
            print(f"处理文件失败 {py_file}: {e}")
    
    if fixed_files:
        print(f"✅ 修复了 {len(fixed_files)} 个文件的参数错误")
        for file in fixed_files[:10]:  # 只显示前10个
            print(f"   - {file}")
        if len(fixed_files) > 10:
            print(f"   ... 还有 {len(fixed_files) - 10} 个文件")
    else:
        print("✅ 没有发现参数错误")

def fix_specific_files():
    """修复特定文件的已知问题"""
    print("🔧 修复特定文件的已知问题...")
    
    # 修复network_simulator.py
    network_sim_file=Path('experiments/network_simulator.py')
    if network_sim_file.exists():
        with open(network_sim_file, 'r', encoding='utf-8') as f:
            content=f.read()
        
        # 修复参数名
        content=content.replace("'--client-nums'", "'--client-nums'")
        content=content.replace("'--communication-rounds'", "'--communication-rounds'")
        content=content.replace("'--output-dir'", "'--output-dir'")
        
        with open(network_sim_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ 修复 network_simulator.py")
    
    # 修复experiment_monitor.py
    monitor_file=Path('experiments/experiment_monitor.py')
    if monitor_file.exists():
        with open(monitor_file, 'r', encoding='utf-8') as f:
            content=f.read()
        
        # 修复参数声明
        content=re.sub(r'type\s*=\s*str', 'type=str', content)
        
        with open(monitor_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ 修复 experiment_monitor.py")
    
    # 修复master_experiment_runner.py
    master_file=Path('experiments/master_experiment_runner.py')
    if master_file.exists():
        with open(master_file, 'r', encoding='utf-8') as f:
            content=f.read()
        
        # 修复参数名
        content=content.replace("'--output-dir'", "'--output-dir'")
        content=re.sub(r'type\s*=\s*str', 'type=str', content)
        
        with open(master_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("   ✅ 修复 master_experiment_runner.py")

def validate_fixes():
    """验证修复结果"""
    print("🧪 验证修复结果...")
    
    # 检查是否还有参数错误
    python_files=list(Path('.').rglob('*.py'))
    error_files=[]
    
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content=f.read()
            
            # 检查是否还有空格参数名
            if re.search(r'--[a-zA-Z]+\s+-\s+[a-zA-Z]+', content):
                error_files.append(str(py_file))
            
            # 检查是否还有空格赋值
            if re.search(r'type\s*=\s*[a-zA-Z]', content):
                error_files.append(str(py_file))
                
        except Exception as e:
            print(f"验证文件失败 {py_file}: {e}")
    
    if error_files:
        print(f"⚠️ 仍有 {len(error_files)} 个文件存在问题:")
        for file in error_files:
            print(f"   - {file}")
    else:
        print("✅ 所有参数错误已修复")

def main():
    """主函数"""
    print("🚀 开始修复参数错误...")
    print("=" * 50)
    
    # 1. 批量修复
    fix_argument_errors()
    
    # 2. 修复特定文件
    fix_specific_files()
    
    # 3. 验证修复结果
    validate_fixes()
    
    print("=" * 50)
    print("🎉 参数错误修复完成！")

if __name__== "__main__":
    main()
