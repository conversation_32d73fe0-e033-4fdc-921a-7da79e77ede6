"""
中文字体配置工具
解决matplotlib中文显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os
import logging

logger=logging.getLogger(__name__)

def setup_chinese_font():
    """配置matplotlib中文字体支持"""
    try:
        system=platform.system()
        
        # 根据操作系统选择合适的中文字体
        if system== "Windows":
            fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
        elif system== "Darwin":  # macOS
            fonts = ['Arial Unicode MS', 'Heiti TC', 'PingFang SC']
        else:  # Linux
            fonts=['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']
        
        # 尝试设置字体
        font_set=False
        for font_name in fonts:
            try:
                plt.rcParams['font.sans - serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                
                # 测试字体是否可用
                fig, ax=plt.subplots(figsize=(1, 1))
                ax.text(0.5, 0.5, '测试', fontsize=12)
                plt.close(fig)
                
                logger.info(f"✅ 成功设置中文字体: {font_name}")
                font_set=True
                break
                
            except Exception as e:
                logger.debug(f"字体 {font_name} 不可用: {e}")
                continue
        
        if not font_set:
            # 如果所有字体都不可用，使用英文替代
            logger.warning("⚠️ 无法设置中文字体，将使用英文标签")
            plt.rcParams['font.sans - serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 字体配置失败: {e}")
        return False

def get_safe_labels(chinese_labels, english_labels=None):
    """
    获取安全的标签（如果中文字体不可用则使用英文）
    
    Args:
        chinese_labels: 中文标签列表或字典
        english_labels: 英文标签列表或字典（可选）
    
    Returns:
        安全的标签
    """
    if setup_chinese_font():
        return chinese_labels
    else:
        if english_labels is not None:
            return english_labels
        else:
            # 如果没有提供英文标签，尝试转换中文标签
            if isinstance(chinese_labels, dict):
                return {k: f"Label_{i}" for i, k in enumerate(chinese_labels.keys())}
            elif isinstance(chinese_labels, list):
                return [f"Label_{i}" for i in range(len(chinese_labels))]
            else:
                return "Label"

# 预定义的中英文标签映射
LABEL_MAPPING={
    # 实验相关
    '准确率': 'Accuracy',
    '损失': 'Loss', 
    '轮次': 'Round',
    '客户端数量': 'Client Count',
    '通信轮次': 'Communication Round',
    '训练时间': 'Training Time',
    '收敛轮次': 'Convergence Round',
    
    # 算法相关
    'FedAvg': 'FedAvg',
    'FedProx': 'FedProx', 
    'VeryFL': 'VeryFL',
    'VeryFL - PoL': 'VeryFL - PoL',
    
    # 数据集相关
    'CIFAR - 10': 'CIFAR - 10',
    'CIFAR - 100': 'CIFAR - 100',
    'FashionMNIST': 'FashionMNIST',
    'EMNIST': 'EMNIST',
    
    # 性能指标
    '平均准确率': 'Average Accuracy',
    '最终准确率': 'Final Accuracy',
    '通信开销': 'Communication Cost',
    '计算开销': 'Computation Cost',
    '内存使用': 'Memory Usage',
    
    # PoL相关
    '学习证明验证率': 'PoL Verification Rate',
    '证明生成时间': 'Proof Generation Time',
    '证明验证时间': 'Proof Verification Time',
    '区块链交互次数': 'Blockchain Interactions',
    '激励分配': 'Incentive Distribution'
}

def translate_label(chinese_label):
    """翻译中文标签为英文"""
    return LABEL_MAPPING.get(chinese_label, chinese_label)

def get_plot_labels(labels):
    """获取绘图用的安全标签"""
    if isinstance(labels, dict):
        chinese_labels=labels
        english_labels = {k: translate_label(v) for k, v in labels.items()}
    elif isinstance(labels, list):
        chinese_labels=labels
        english_labels = [translate_label(label) for label in labels]
    else:
        chinese_labels=labels
        english_labels = translate_label(labels)
    
    return get_safe_labels(chinese_labels, english_labels)

# 在模块导入时自动配置字体
setup_chinese_font()
