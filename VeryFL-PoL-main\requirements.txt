# VeryFL-PoL 项目依赖
# 可验证联邦学习框架的完整依赖列表

# ===== 核心深度学习框架 =====
torch>=1.13.0,<2.0.0
torchvision>=0.14.0,<1.0.0
numpy>=1.21.0,<2.0.0
scipy>=1.7.0,<2.0.0

# ===== 机器学习和数据处理 =====
scikit-learn>=1.0.0,<2.0.0
pandas>=1.3.0,<2.0.0

# ===== 可视化和分析 =====
matplotlib>=3.5.0,<4.0.0
seaborn>=0.11.0,<1.0.0
plotly>=5.0.0,<6.0.0

# ===== 配置和序列化 =====
pyyaml>=6.0,<7.0
pickle-mixin>=1.0.2

# ===== 进度条和用户界面 =====
tqdm>=4.62.0,<5.0.0
rich>=12.0.0,<14.0.0

# ===== 区块链相关 =====
eth-brownie>=1.10.0,<2.0.0
web3>=6.0.0,<7.0.0
py-solc-x>=1.12.0,<2.0.0

# ===== 系统监控和性能 =====
psutil>=5.8.0,<6.0.0
GPUtil>=1.4.0

# ===== 实验和分析工具 =====
jupyter>=1.0.0,<2.0.0
ipython>=7.0.0,<9.0.0
notebook>=6.4.0,<7.0.0

# ===== 统计分析 =====
statsmodels>=0.13.0,<1.0.0

# ===== 网络和通信 =====
requests>=2.25.0,<3.0.0
urllib3>=1.26.0,<2.0.0

# ===== 日志和调试 =====
colorlog>=6.0.0,<7.0.0

# ===== 数据集处理 =====
Pillow>=8.0.0,<10.0.0
opencv-python>=4.5.0,<5.0.0

# ===== 并发和异步 =====
asyncio-mqtt>=0.11.0,<1.0.0

# ===== 开发工具（可选） =====
# pytest>=6.0.0,<8.0.0
# black>=22.0.0,<24.0.0
# flake8>=4.0.0,<6.0.0

# ===== 特殊说明 =====
# 1. 如果在CPU环境下运行，torch会自动使用CPU版本
# 2. 区块链功能需要Node.js环境和Ganache
# 3. 某些可视化功能可能需要额外的系统依赖
