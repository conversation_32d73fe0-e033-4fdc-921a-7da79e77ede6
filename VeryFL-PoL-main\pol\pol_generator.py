"""
PoL生成器模块
负责在训练过程中生成学习证明
"""

import torch
import numpy as np
import os
import logging
import threading
import queue
import time
import hashlib
import pickle
from typing import List, Dict, Any, Optional
from copy import deepcopy
from concurrent.futures import ThreadPoolExecutor
from .pol_utils import PoLUtils

logger=logging.getLogger(__name__)

class PoLGenerator:
    """学习证明生成器"""
    
    def __init__(self, save_freq: int=None, save_dir: str=None,
                 enable_compression: bool=True, steps_per_epoch: int=None,
                 enable_async_save: bool=True, enable_disk_save: bool=False):
        """
        初始化PoL生成器

        Args:
            save_freq: 保存频率
            save_dir: 保存目录（None时使用统一路径管理器）
            enable_compression: 是否启用压缩
            steps_per_epoch: 每轮步数
            enable_async_save: 是否启用异步保存
            enable_disk_save: 是否保存到磁盘
        """
        # 导入路径管理器
        from util.path_manager import path_manager

        # 设置保存目录
        if save_dir is None:
            save_dir=str(path_manager.get_path('pol_data'))
        self.save_freq=save_freq
        self.steps_per_epoch = steps_per_epoch
        self.save_dir = save_dir
        self.enable_compression = enable_compression
        self.enable_async_save = enable_async_save
        self.enable_disk_save = enable_disk_save
        self.checkpoints = []  # 存储检查点信息
        self.batch_indices = []  # 存储批次索引序列
        self.model_updates = []  # 存储模型更新
        self.step_count = 0
        self.is_training = False

        # 压缩相关
        self.initial_model_state = None  # 初始模型状态
        self.previous_model_state = None  # 上一个检查点的模型状态

        # 异步保存相关
        self.checkpoint_queue = queue.Queue() if enable_async_save else None
        self.save_thread=None
        self.stop_save_thread = threading.Event() if enable_async_save else None
        self.pending_saves=0  # 待保存的检查点数量
        self.client_id = None  # 客户端ID，在start_training时设置

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        logger.info(f"PoL生成器初始化完成，保存频率: {save_freq}, 保存目录: {save_dir}, "
                   f"压缩模式: {'启用' if enable_compression else '禁用'}, "
                   f"异步保存: {'启用' if enable_async_save else '禁用'}, "
                   f"磁盘保存: {'启用' if enable_disk_save else '禁用(仅内存)'}")
    
    def start_training(self, model: torch.nn.Module, client_id: str, steps_per_epoch: int=None):
        """开始训练并初始化PoL生成"""
        self.client_id=client_id

        # 严格按照PoL论文：k = S（每epoch保存一次）
        # 论文第919 - 921行：k = S是基线设置，存储开销为1×（无额外开销）
        if self.save_freq is None:
            if steps_per_epoch is not None:
                self.save_freq = steps_per_epoch
                logger.info(f"按PoL论文设置k = S: 每epoch保存一次，k={self.save_freq}步")
            elif self.steps_per_epoch is not None:
                self.save_freq=self.steps_per_epoch
                logger.info(f"按PoL论文设置k = S: 每epoch保存一次，k={self.save_freq}步")
            else:
                self.save_freq=100  # 默认值，但会警告
                logger.warning(f"⚠️ 无法确定每epoch步数，使用默认k={self.save_freq}，这可能不符合论文建议")

        self.is_training=True
        self.step_count = 0
        self.checkpoints = []
        self.batch_indices = []
        self.model_updates = []

        # 保存初始模型状态（PoL证明必须独立拷贝）
        self.initial_model_state = {k: v.clone().detach() for k, v in model.state_dict().items()}
        # previous_model_state用于计算差值，使用引用节省内存
        self.previous_model_state=model.state_dict()  # 引用，仅用于计算

        # 保存初始检查点（修复：即使在压缩模式下也要保存初始状态）
        initial_checkpoint={
            'step': 0,
            'model_state': {k: v.clone().detach() for k, v in model.state_dict().items()},  # 轻量级拷贝
            'is_initial': True,
            'timestamp': torch.tensor(0.0),
            'compressed': False  # 初始检查点不压缩
        }

        self.checkpoints.append(initial_checkpoint)

        # 启动异步保存线程
        if self.enable_async_save:
            self._start_async_save_thread()

        logger.info(f"客户端 {client_id} 开始PoL训练 (压缩模式: {self.enable_compression}, 保存频率: {self.save_freq}步)")
        return True

    def _start_async_save_thread(self):
        """启动异步保存工作线程"""
        if self.save_thread is None or not self.save_thread.is_alive():
            self.stop_save_thread.clear()
            self.save_thread=threading.Thread(target = self._async_save_worker, daemon=True)
            self.save_thread.start()
            logger.debug("异步保存线程已启动")

    def _async_save_worker(self):
        """异步保存工作线程"""
        while not self.stop_save_thread.is_set():
            try:
                # 等待检查点数据，超时1秒
                checkpoint_data=self.checkpoint_queue.get(timeout = 1.0)

                # 执行实际的保存操作
                self._save_checkpoint_to_disk(checkpoint_data)

                # 减少待保存计数
                self.pending_saves -= 1

                # 标记任务完成
                self.checkpoint_queue.task_done()

            except queue.Empty:
                # 超时，继续循环
                continue
            except Exception as e:
                logger.error(f"异步保存检查点时出错: {e}")
                self.pending_saves -= 1

    def _save_checkpoint_to_disk(self, checkpoint_data):
        """将检查点数据保存到磁盘"""
        try:
            client_id=checkpoint_data['client_id']
            step = checkpoint_data['step']
            data = checkpoint_data['data']

            # 生成文件名
            filename = f"checkpoint_{client_id}_step_{step}.pkl"
            filepath = os.path.join(self.save_dir, filename)

            # 保存到磁盘
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)

            logger.debug(f"检查点已保存: {filepath}")

        except Exception as e:
            logger.error(f"保存检查点到磁盘失败: {e}")

    def _stop_async_save_thread(self):
        """停止异步保存线程（优化：非阻塞版本）"""
        if self.enable_async_save and self.save_thread:
            # 设置停止标志，但不等待所有保存完成（避免阻塞）
            self.stop_save_thread.set()

            # 给线程一个短暂的时间来完成当前操作
            self.save_thread.join(timeout=2.0)

            if self.save_thread.is_alive():
                logger.warning(f"异步保存线程未能在2秒内停止，但训练将继续")
            else:
                logger.debug("异步保存线程已停止")

            # 重置状态
            self.pending_saves=0
    
    def record_batch(self, batch_data: torch.Tensor, batch_targets: torch.Tensor,
                    batch_idx: int, epoch: int):
        """记录批次信息"""
        if not self.is_training:
            return

        batch_info={
            'epoch': epoch,
            'batch_idx': batch_idx,
            'step': self.step_count + 1,  # 下一步的步数
            'data_hash': PoLUtils.compute_hash(batch_data),
            'target_hash': PoLUtils.compute_hash(batch_targets),
            'batch_size': batch_data.shape[0]
        }
        self.batch_indices.append(batch_info)
    
    def record_model_update(self, model: torch.nn.Module, optimizer: torch.optim.Optimizer,
                           loss: float, epoch: int, batch_idx: int):
        """记录模型更新信息"""
        if not self.is_training:
            return
        
        self.step_count += 1
        
        # 记录模型更新信息
        update_info={
            'step': self.step_count,
            'epoch': epoch,
            'batch_idx': batch_idx,
            'loss': loss,
            'model_params_hash': PoLUtils.compute_hash(PoLUtils.get_parameters(model))
        }
        self.model_updates.append(update_info)
        
        # 根据保存频率决定是否保存检查点
        if self.step_count % self.save_freq== 0:
            if self.enable_compression:
                # 压缩模式：只保存参数变化
                checkpoint = self._create_compressed_checkpoint(model, optimizer, loss, epoch, batch_idx)
            else:
                # 非压缩模式：保存完整状态（优化：使用轻量级拷贝）
                checkpoint={
                    'step': self.step_count,
                    'model_state': {k: v.clone().detach() for k, v in model.state_dict().items()},
                    'optimizer_state': {k: v.clone().detach() if torch.is_tensor(v) else v
                                      for k, v in optimizer.state_dict().items()} if optimizer else None,
                    'loss': loss,
                    'epoch': epoch,
                    'batch_idx': batch_idx,
                    'compressed': False
                }

            # 保存检查点（根据磁盘保存设置决定是否写入文件）
            if self.enable_async_save and self.enable_disk_save:
                self._async_save_checkpoint(checkpoint)
            else:
                # 仅保存到内存（不写入磁盘文件）
                self.checkpoints.append(checkpoint)

            # 更新previous_model_state（优化：使用轻量级拷贝）
            if self.enable_compression:
                self.previous_model_state={k: v.clone().detach() for k, v in model.state_dict().items()}

            logger.debug(f"保存检查点: step {self.step_count} (压缩: {self.enable_compression}, 异步: {self.enable_async_save})")

    def _async_save_checkpoint(self, checkpoint):
        """异步保存检查点"""
        try:
            # 将检查点添加到内存列表（立即返回，不阻塞训练）
            self.checkpoints.append(checkpoint)

            # 如果启用了异步保存到磁盘，将数据加入队列
            if self.checkpoint_queue is not None and self.client_id is not None:
                checkpoint_data={
                    'client_id': self.client_id,
                    'step': self.step_count,
                    'data': checkpoint
                }
                self.checkpoint_queue.put(checkpoint_data)
                self.pending_saves += 1

        except Exception as e:
            logger.error(f"异步保存检查点失败: {e}")
            # 确保检查点至少保存到内存中
            if checkpoint not in self.checkpoints:
                self.checkpoints.append(checkpoint)

    def _create_compressed_checkpoint(self, model: torch.nn.Module, optimizer: torch.optim.Optimizer,
                                     loss: float, epoch: int, batch_idx: int) -> Dict[str, Any]:
        """创建压缩检查点（只保存参数变化）"""
        current_state=model.state_dict()

        # 计算参数变化（增量）- 使用引用计算，节省内存
        parameter_deltas={}
        for key in current_state:
            if key in self.previous_model_state:
                # 直接计算差值，不额外拷贝
                delta = current_state[key] - self.previous_model_state[key]
                # 只保存有显著变化的参数，这里必须拷贝到检查点中
                if torch.norm(delta) > 1e-8:  # 阈值可调
                    parameter_deltas[key] = delta.clone().detach()  # 检查点必须独立拷贝

        # 创建压缩检查点
        checkpoint={
            'step': self.step_count,
            'parameter_deltas': parameter_deltas,  # 只保存变化量
            'optimizer_state': {k: v.clone().detach() if torch.is_tensor(v) else v
                              for k, v in optimizer.state_dict().items()} if optimizer else None,
            'loss': loss,
            'epoch': epoch,
            'batch_idx': batch_idx,
            'compressed': True,
            'compression_ratio': len(parameter_deltas) / len(current_state)  # 压缩比
        }

        # 更新previous_model_state引用到当前状态（用于下次计算差值）
        self.previous_model_state=current_state

        return checkpoint

    def _reconstruct_model_state(self, checkpoint_index: int) -> Dict[str, torch.Tensor]:
        """从压缩检查点重构完整模型状态"""
        if not self.enable_compression:
            # 非压缩模式直接返回
            return self.checkpoints[checkpoint_index]['model_state']

        # 从初始状态开始重构
        reconstructed_state=deepcopy(self.initial_model_state)

        # 应用所有增量变化
        for i in range(1, checkpoint_index + 1):
            checkpoint=self.checkpoints[i]
            if 'parameter_deltas' in checkpoint:
                for key, delta in checkpoint['parameter_deltas'].items():
                    if key in reconstructed_state:
                        reconstructed_state[key] += delta

        return reconstructed_state

    def _calculate_compression_stats(self) -> Dict[str, Any]:
        """计算压缩统计信息"""
        if not self.enable_compression:
            return {'compression_enabled': False}

        total_checkpoints=len(self.checkpoints)
        compressed_checkpoints=sum(1 for cp in self.checkpoints if cp.get('compressed', False))

        # 计算平均压缩比
        compression_ratios=[]
        for cp in self.checkpoints:
            if 'compression_ratio' in cp:
                compression_ratios.append(cp['compression_ratio'])

        avg_compression_ratio=sum(compression_ratios) / len(compression_ratios) if compression_ratios else 0

        # 估算存储节省
        estimated_savings=(1 - avg_compression_ratio) * 100 if avg_compression_ratio > 0 else 0

        return {
            'compression_enabled': True,
            'total_checkpoints': total_checkpoints,
            'compressed_checkpoints': compressed_checkpoints,
            'average_compression_ratio': avg_compression_ratio,
            'estimated_storage_savings_percent': estimated_savings
        }

    def finalize_proof(self, model: torch.nn.Module) -> Dict[str, Any]:
        """完成训练并生成最终的学习证明"""
        if not self.is_training:
            raise RuntimeError("PoL生成器未处于训练状态")

        # 停止异步保存线程，等待所有保存完成
        if self.enable_async_save:
            self._stop_async_save_thread()

        # 保存最终模型状态（如果最后一步不是检查点）
        if self.step_count % self.save_freq != 0:
            if self.enable_compression:
                final_checkpoint=self._create_compressed_checkpoint(
                    model, None, 0.0, 0, self.step_count
                )
            else:
                final_checkpoint={
                    'step': self.step_count,
                    'model_state': {k: v.clone().detach() for k, v in model.state_dict().items()},  # 轻量级拷贝
                    'timestamp': torch.tensor(float(self.step_count)),
                    'compressed': False
                }
            self.checkpoints.append(final_checkpoint)
        
        # 计算压缩统计信息
        compression_stats=self._calculate_compression_stats()

        # 生成PoL证明（修复：添加初始模型状态以支持验证）
        pol_proof={
            'client_id': self.client_id,
            'total_steps': self.step_count,
            'save_freq': self.save_freq,
            'checkpoints': self.checkpoints,
            'batch_indices': self.batch_indices,
            'model_updates': self.model_updates,
            'compression_enabled': self.enable_compression,
            'compression_stats': compression_stats,
            'initial_model_state': self.initial_model_state,  # 添加初始状态
            'proof_hash': None  # 将在下面计算
        }
        
        # 计算整个证明的哈希值
        proof_content={
            'total_steps': pol_proof['total_steps'],
            'save_freq': pol_proof['save_freq'],
            'batch_count': len(pol_proof['batch_indices']),
            'checkpoint_count': len(pol_proof['checkpoints'])
        }
        pol_proof['proof_hash'] = PoLUtils.compute_hash(str(proof_content))
        
        # 保存PoL数据到文件（仅在启用磁盘保存时）
        if self.enable_disk_save:
            pol_file_path=os.path.join(self.save_dir, f"pol_{self.client_id}.pkl")
            PoLUtils.save_pol_data(pol_proof, pol_file_path)
            logger.info(f"PoL证明已保存到磁盘: {pol_file_path}")
        else:
            logger.info(f"PoL证明仅保存在内存中（磁盘保存已禁用）")
        
        self.is_training=False
        logger.info(f"客户端 {self.client_id} PoL证明生成完成，总步数: {self.step_count}")
        
        return pol_proof
    
    def get_proof_summary(self) -> Dict[str, Any]:
        """获取PoL证明的摘要信息"""
        if not self.checkpoints:
            return {}
        
        return {
            'client_id': getattr(self, 'client_id', 'unknown'),
            'total_steps': self.step_count,
            'checkpoint_count': len(self.checkpoints),
            'batch_count': len(self.batch_indices),
            'save_freq': self.save_freq,
            'is_training': self.is_training
        }
    
    def reset(self):
        """重置PoL生成器状态"""
        self.checkpoints=[]
        self.batch_indices = []
        self.model_updates = []
        self.step_count = 0
        self.is_training = False
        logger.info("PoL生成器状态已重置")
